<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>精准曝气控制系统 (GPAC) - 告警管理</title>
    <link rel="stylesheet" href="css/all.min.css">
    <link rel="stylesheet" href="css/style.css">
    <style>
        /* 告警页面容器 */
        .alarm-container {
            padding: 24px;
        }

        /* 告警页面头部 */
        .alarm-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 24px;
            flex-wrap: wrap;
            gap: 16px;
        }

        /* 告警过滤器区域 */
        .alarm-filters {
            display: flex;
            gap: 16px;
            flex-wrap: wrap;
            background-color: white;
            padding: 16px;
            border-radius: 8px;
            box-shadow: 0 2px 8px rgba(0,0,0,0.08);
            flex-grow: 1;
        }

        /* 告警列表容器 */
        .alarm-list {
            background: white;
            border-radius: 12px;
            box-shadow: 0 4px 12px rgba(0,0,0,0.08);
            overflow: hidden;
        }

        /* 告警项 */
        .alarm-item {
            padding: 20px;
            border-bottom: 1px solid var(--border-color);
            display: flex;
            align-items: center;
            justify-content: space-between;
            transition: all 0.3s ease;
            position: relative;
        }

        .alarm-item:hover {
            background-color: #fafafa;
        }

        .alarm-item:last-child {
            border-bottom: none;
        }

        /* 告警信息区域 */
        .alarm-info {
            display: flex;
            align-items: flex-start;
            gap: 16px;
            flex: 1;
        }

        /* 告警图标 */
        .alarm-icon {
            width: 48px;
            height: 48px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            box-shadow: 0 4px 8px rgba(0,0,0,0.15);
            flex-shrink: 0;
            font-size: 20px;
            transition: transform 0.2s ease;
        }

        .alarm-item:hover .alarm-icon {
            transform: scale(1.05);
        }

        .alarm-critical {
            background-color: var(--danger-color);
        }

        .alarm-warning {
            background-color: var(--warning-color);
        }

        .alarm-info-icon {
            background-color: var(--primary-color);
        }

        /* 告警详情 */
        .alarm-details {
            flex-grow: 1;
        }

        .alarm-details h4 {
            font-size: 16px;
            margin-bottom: 8px;
            color: var(--text-color);
            font-weight: 600;
        }

        .alarm-details p {
            color: var(--text-light);
            margin-bottom: 8px;
            line-height: 1.5;
        }

        .alarm-details small {
            color: #999;
            font-size: 12px;
        }

        /* 告警操作区域 */
        .alarm-actions {
            display: flex;
            gap: 12px;
            align-items: center;
            flex-shrink: 0;
        }

        /* 告警徽章 */
        .badge {
            padding: 6px 12px;
            border-radius: 16px;
            font-size: 12px;
            font-weight: 500;
            display: inline-flex;
            align-items: center;
            justify-content: center;
            min-width: 60px;
            text-align: center;
        }

        .badge-warning {
            background-color: #fff7e6;
            color: var(--warning-color);
            border: 1px solid #ffd591;
        }

        .badge-critical {
            background-color: #fff1f0;
            color: var(--danger-color);
            border: 1px solid #ffa39e;
        }

        .badge-info {
            background-color: #e6f7ff;
            color: var(--primary-color);
            border: 1px solid #91d5ff;
        }

        /* 按钮样式优化 */
        .btn-sm {
            padding: 6px 12px;
            font-size: 13px;
            border-radius: 4px;
        }

        .btn-secondary {
            background-color: #f0f0f0;
            color: #666;
        }

        .btn-secondary:hover {
            background-color: #e0e0e0;
        }

        /* 分页控件 */
        .pagination {
            display: flex;
            justify-content: center;
            margin-top: 24px;
            gap: 8px;
        }

        .pagination-item {
            width: 32px;
            height: 32px;
            display: flex;
            align-items: center;
            justify-content: center;
            border-radius: 4px;
            background-color: white;
            cursor: pointer;
            transition: all 0.2s;
            border: 1px solid var(--border-color);
        }

        .pagination-item:hover {
            border-color: var(--primary-color);
            color: var(--primary-color);
        }

        .pagination-item.active {
            background-color: var(--primary-color);
            color: white;
            border-color: var(--primary-color);
        }

        /* 响应式调整 */
        @media (max-width: 768px) {
            .alarm-item {
                flex-direction: column;
                align-items: flex-start;
            }

            .alarm-actions {
                margin-top: 16px;
                align-self: flex-end;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <!-- 顶部栏 -->
        <div class="topbar">
            <div class="topbar-left">
                <div class="toggle-sidebar" onclick="toggleSidebar()">
                    <i class="fas fa-bars"></i>
                </div>
                <img src="logo.png" alt="公司Logo" class="topbar-logo">
                <h2 class="topbar-title">精准曝气控制系统</h2>
            </div>
            <div class="topbar-right">
                <div class="user-dropdown">
                    <div class="user-info">
                        <div class="user-avatar">管</div>
                        <span>管理员</span>
                        <i class="fas fa-chevron-down" style="margin-left: 5px; font-size: 12px;"></i>
                    </div>
                    <div class="dropdown-content">
                        <a href="#"><i class="fas fa-user-circle"></i> 个人信息</a>
                        <a href="#"><i class="fas fa-cog"></i> 账号设置</a>
                        <a href="index.html" onclick="logout()"><i class="fas fa-sign-out-alt"></i> 退出登录</a>
                    </div>
                </div>
            </div>
        </div>

        <!-- 主体内容区域 -->
        <div class="main-container">
            <!-- 侧边栏 -->
            <div class="sidebar">
                <div class="sidebar-menu">
                    <div class="menu-item" id="menu-dashboard" onclick="window.location.href='dashboard.html'">
                        <i class="fas fa-tachometer-alt"></i>
                        <span>实时监控</span>
                    </div>
                    <div class="menu-item" id="menu-model" onclick="window.location.href='model-control.html'">
                        <i class="fas fa-brain"></i>
                        <span>模型控制</span>
                    </div>
                    <div class="menu-item" id="menu-scheduling" onclick="window.location.href='scheduling.html'">
                        <i class="fas fa-tasks"></i>
                        <span>调度管理</span>
                    </div>
                    <div class="menu-item" id="menu-reports" onclick="window.location.href='reports.html'">
                        <i class="fas fa-chart-bar"></i>
                        <span>数据报表</span>
                    </div>
                    <div class="menu-item active" id="menu-alarms" onclick="window.location.href='alarms.html'">
                        <i class="fas fa-bell"></i>
                        <span>告警管理</span>
                    </div>
                    <div class="menu-item" id="menu-settings" onclick="window.location.href='settings.html'">
                        <i class="fas fa-cog"></i>
                        <span>系统配置</span>
                    </div>
                </div>
            </div>

            <!-- 内容区域 -->
            <div class="content" style="margin-left: var(--sidebar-width); width: calc(100% - var(--sidebar-width)); padding: 20px;">
                <div class="alarm-container">
                <div class="alarm-header">
                    <div class="alarm-filters">
                        <div class="form-group">
                            <select class="form-control">
                                <option>全部级别</option>
                                <option>紧急</option>
                                <option>警告</option>
                                <option>提示</option>
                            </select>
                        </div>
                        <div class="form-group">
                            <select class="form-control">
                                <option>全部状态</option>
                                <option>未处理</option>
                                <option>处理中</option>
                                <option>已解决</option>
                            </select>
                        </div>
                        <div class="form-group">
                            <input type="text" class="form-control" placeholder="搜索告警">
                        </div>
                        <button class="btn btn-primary">
                            <i class="fas fa-search"></i>
                            查询
                        </button>
                    </div>
                    <button class="btn btn-primary">
                        <i class="fas fa-cog"></i>
                        告警设置
                    </button>
                </div>

                <div class="alarm-list">
                    <!-- 紧急告警 -->
                    <div class="alarm-item">
                        <div class="alarm-info">
                            <div class="alarm-icon alarm-critical">
                                <i class="fas fa-exclamation-triangle"></i>
                            </div>
                            <div class="alarm-details">
                                <h4>DO浓度异常</h4>
                                <p>A区DO浓度低于阈值(1.2mg/L)，当前值：0.8mg/L</p>
                                <small><i class="far fa-clock"></i> 2025-05-16 10:23:45</small>
                            </div>
                        </div>
                        <div class="alarm-actions">
                            <span class="badge badge-critical">紧急</span>
                            <button class="btn btn-primary btn-sm">
                                <i class="fas fa-check-circle"></i> 处理
                            </button>
                            <button class="btn btn-secondary btn-sm">
                                <i class="fas fa-ban"></i> 忽略
                            </button>
                        </div>
                    </div>

                    <!-- 警告告警 -->
                    <div class="alarm-item">
                        <div class="alarm-info">
                            <div class="alarm-icon alarm-warning">
                                <i class="fas fa-exclamation-circle"></i>
                            </div>
                            <div class="alarm-details">
                                <h4>鼓风机能耗偏高</h4>
                                <p>2号鼓风机能耗超过预期20%，建议检查运行状态</p>
                                <small><i class="far fa-clock"></i> 2025-05-16 09:15:30</small>
                            </div>
                        </div>
                        <div class="alarm-actions">
                            <span class="badge badge-warning">警告</span>
                            <button class="btn btn-primary btn-sm">
                                <i class="fas fa-check-circle"></i> 处理
                            </button>
                            <button class="btn btn-secondary btn-sm">
                                <i class="fas fa-ban"></i> 忽略
                            </button>
                        </div>
                    </div>

                    <!-- 提示告警 -->
                    <div class="alarm-item">
                        <div class="alarm-info">
                            <div class="alarm-icon alarm-info-icon">
                                <i class="fas fa-info-circle"></i>
                            </div>
                            <div class="alarm-details">
                                <h4>模型预测偏差</h4>
                                <p>B区NH₄⁺浓度预测模型偏差超过10%，建议重新校准</p>
                                <small><i class="far fa-clock"></i> 2025-05-16 08:45:12</small>
                            </div>
                        </div>
                        <div class="alarm-actions">
                            <span class="badge badge-info">提示</span>
                            <button class="btn btn-primary btn-sm">
                                <i class="fas fa-check-circle"></i> 处理
                            </button>
                            <button class="btn btn-secondary btn-sm">
                                <i class="fas fa-ban"></i> 忽略
                            </button>
                        </div>
                    </div>

                    <!-- 更多告警项... -->
                    <div class="alarm-item">
                        <div class="alarm-info">
                            <div class="alarm-icon alarm-warning">
                                <i class="fas fa-exclamation-circle"></i>
                            </div>
                            <div class="alarm-details">
                                <h4>水泵压力波动</h4>
                                <p>1号水泵压力波动超过正常范围，建议检查管道是否堵塞</p>
                                <small><i class="far fa-clock"></i> 2025-05-16 08:30:22</small>
                            </div>
                        </div>
                        <div class="alarm-actions">
                            <span class="badge badge-warning">警告</span>
                            <button class="btn btn-primary btn-sm">
                                <i class="fas fa-check-circle"></i> 处理
                            </button>
                            <button class="btn btn-secondary btn-sm">
                                <i class="fas fa-ban"></i> 忽略
                            </button>
                        </div>
                    </div>
                </div>

                <!-- 分页控件 -->
                <div class="pagination">
                    <div class="pagination-item">
                        <i class="fas fa-angle-left"></i>
                    </div>
                    <div class="pagination-item active">1</div>
                    <div class="pagination-item">2</div>
                    <div class="pagination-item">3</div>
                    <div class="pagination-item">
                        <i class="fas fa-angle-right"></i>
                    </div>
                </div>
                </div>
            </div>
        </div>
    </div>

    <script src="js/common.js"></script>
    <script>
        // 初始化页面
        document.addEventListener('DOMContentLoaded', function() {
            // 加载公共元素
            loadCommonElements();

            // 为告警项添加点击效果
            setupAlarmItems();

            // 为分页项添加点击效果
            setupPagination();
        });

        // 设置告警项的交互效果
        function setupAlarmItems() {
            const alarmItems = document.querySelectorAll('.alarm-item');

            alarmItems.forEach(item => {
                // 处理按钮点击事件
                const processBtn = item.querySelector('.btn-primary');
                if (processBtn) {
                    processBtn.addEventListener('click', function(e) {
                        e.stopPropagation(); // 阻止事件冒泡
                        const alarmTitle = item.querySelector('h4').textContent;
                        const alarmId = 'ALM-' + Math.floor(Math.random() * 10000);

                        // 显示处理中通知
                        showNotification(`正在准备处理告警: ${alarmTitle}`, 'info');

                        // 添加加载动画
                        const originalBtnContent = this.innerHTML;
                        this.innerHTML = '<i class="fas fa-spinner fa-spin"></i> 处理中...';
                        this.disabled = true;

                        // 模拟加载过程，然后跳转到处理页面
                        setTimeout(() => {
                            // 创建一个表单来传递数据到处理页面
                            const form = document.createElement('form');
                            form.method = 'GET';
                            form.action = 'alarm-process.html';

                            // 添加告警信息作为参数
                            const params = {
                                id: alarmId,
                                title: alarmTitle,
                                description: item.querySelector('p').textContent,
                                time: item.querySelector('small').textContent.replace(/^\s+|\s+$/g, ''),
                                level: item.querySelector('.badge').textContent
                            };

                            // 为每个参数创建隐藏的输入字段
                            for (const key in params) {
                                const input = document.createElement('input');
                                input.type = 'hidden';
                                input.name = key;
                                input.value = params[key];
                                form.appendChild(input);
                            }

                            // 添加表单到文档并提交
                            document.body.appendChild(form);
                            form.submit();
                        }, 800);
                    });
                }

                // 忽略按钮点击事件
                const ignoreBtn = item.querySelector('.btn-secondary');
                if (ignoreBtn) {
                    ignoreBtn.addEventListener('click', function(e) {
                        e.stopPropagation(); // 阻止事件冒泡
                        const alarmTitle = item.querySelector('h4').textContent;

                        // 添加确认动画效果
                        this.innerHTML = '<i class="fas fa-spinner fa-spin"></i> 忽略中';
                        this.disabled = true;

                        // 模拟处理过程
                        setTimeout(() => {
                            item.style.opacity = '0';
                            item.style.height = '0';
                            item.style.padding = '0';
                            item.style.margin = '0';
                            item.style.overflow = 'hidden';

                            // 移除元素
                            setTimeout(() => {
                                item.remove();
                                showNotification(`告警 "${alarmTitle}" 已忽略`, 'success');
                            }, 300);
                        }, 800);
                    });
                }

                // 为告警内容区域添加点击事件（不包括按钮区域）
                const alarmInfo = item.querySelector('.alarm-info');
                if (alarmInfo) {
                    alarmInfo.addEventListener('click', function(e) {
                        // 确保点击不是来自按钮
                        if (e.target.closest('.btn') || e.target.closest('.alarm-actions')) {
                            return;
                        }

                        const parentItem = this.closest('.alarm-item');

                        // 如果已经展开，则收起
                        if (parentItem.classList.contains('expanded')) {
                            parentItem.classList.remove('expanded');
                            const detailPanel = parentItem.querySelector('.alarm-detail-panel');
                            if (detailPanel) {
                                detailPanel.remove();
                            }
                            return;
                        }

                        // 移除其他展开的项
                        document.querySelectorAll('.alarm-item.expanded').forEach(expandedItem => {
                            if (expandedItem !== parentItem) {
                                expandedItem.classList.remove('expanded');
                                const detailPanel = expandedItem.querySelector('.alarm-detail-panel');
                                if (detailPanel) {
                                    detailPanel.remove();
                                }
                            }
                        });

                        // 展开当前项
                        parentItem.classList.add('expanded');

                        // 创建详情面板
                        const detailPanel = document.createElement('div');
                        detailPanel.className = 'alarm-detail-panel';
                        detailPanel.innerHTML = `
                            <div class="alarm-detail-content">
                                <h4>告警详情</h4>
                                <div class="detail-row">
                                    <span class="detail-label">告警ID:</span>
                                    <span class="detail-value">ALM-${Math.floor(Math.random() * 10000)}</span>
                                </div>
                                <div class="detail-row">
                                    <span class="detail-label">设备ID:</span>
                                    <span class="detail-value">DEV-${Math.floor(Math.random() * 100)}</span>
                                </div>
                                <div class="detail-row">
                                    <span class="detail-label">告警时间:</span>
                                    <span class="detail-value">${parentItem.querySelector('small').textContent.replace(/^\s+|\s+$/g, '')}</span>
                                </div>
                                <div class="detail-row">
                                    <span class="detail-label">告警描述:</span>
                                    <span class="detail-value">${parentItem.querySelector('p').textContent}</span>
                                </div>
                                <div class="detail-row">
                                    <span class="detail-label">处理建议:</span>
                                    <span class="detail-value">请检查相关设备运行状态，确认参数是否在正常范围内。如有异常，请联系技术支持。</span>
                                </div>
                                <div class="detail-actions">
                                    <button class="btn btn-primary" onclick="window.location.href='alarm-process.html?id=ALM-${Math.floor(Math.random() * 10000)}'">
                                        <i class="fas fa-tools"></i> 详细处理
                                    </button>
                                </div>
                            </div>
                        `;

                        // 添加到告警项中
                        parentItem.appendChild(detailPanel);

                        // 添加动画效果
                        setTimeout(() => {
                            detailPanel.style.maxHeight = '300px';
                            detailPanel.style.opacity = '1';
                        }, 10);
                    });
                }
            });
        }

        // 设置分页交互效果
        function setupPagination() {
            const paginationItems = document.querySelectorAll('.pagination-item');

            paginationItems.forEach(item => {
                item.addEventListener('click', function() {
                    // 如果已经是激活状态，则不做任何操作
                    if (this.classList.contains('active')) {
                        return;
                    }

                    // 移除其他项的激活状态
                    paginationItems.forEach(pItem => {
                        pItem.classList.remove('active');
                    });

                    // 如果是数字页码，则激活
                    if (!this.querySelector('i')) {
                        this.classList.add('active');
                    }

                    // 显示加载提示
                    showNotification(`正在加载第 ${this.textContent.trim() || '新'} 页数据...`, 'info');

                    // 模拟页面切换效果
                    const alarmList = document.querySelector('.alarm-list');
                    alarmList.style.opacity = '0.5';

                    setTimeout(() => {
                        alarmList.style.opacity = '1';
                        showNotification('数据加载完成', 'success');
                    }, 800);
                });
            });
        }
    </script>
    <style>
        /* 告警详情面板样式 */
        .alarm-item {
            cursor: pointer;
            transition: all 0.3s ease;
        }

        .alarm-item.expanded {
            background-color: #f5f5f5;
        }

        .alarm-detail-panel {
            max-height: 0;
            overflow: hidden;
            opacity: 0;
            transition: all 0.3s ease;
            margin-top: 10px;
            width: 100%;
        }

        .alarm-detail-content {
            background-color: white;
            border-radius: 8px;
            padding: 16px;
            margin-top: 10px;
            box-shadow: inset 0 0 8px rgba(0,0,0,0.05);
            border: 1px solid var(--border-color);
        }

        .alarm-detail-content h4 {
            margin-bottom: 16px;
            color: var(--text-color);
            font-weight: 600;
            border-bottom: 1px solid var(--border-color);
            padding-bottom: 8px;
        }

        .detail-row {
            display: flex;
            margin-bottom: 12px;
        }

        .detail-label {
            font-weight: 500;
            width: 100px;
            color: var(--text-light);
        }

        .detail-value {
            flex: 1;
        }

        .detail-actions {
            margin-top: 20px;
            display: flex;
            justify-content: flex-end;
            gap: 10px;
        }

        /* 禁用按钮样式 */
        .btn:disabled {
            opacity: 0.7;
            cursor: not-allowed;
        }
    </style>
</body>
</html>
