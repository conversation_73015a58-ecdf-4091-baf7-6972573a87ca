<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>精准曝气控制系统 (GPAC) - 登录</title>
    <link rel="stylesheet" href="css/all.min.css">
    <link rel="stylesheet" href="css/style.css">
</head>
<body>
    <div class="login-container">
        <div class="login-form">
            <div class="login-header">
                <img src="logo.png" alt="公司Logo" class="login-logo-img">
                <h1 class="login-title">精准曝气控制系统</h1>
            </div>
            <form id="loginForm">
                <div class="form-group">
                    <label for="username" class="form-label">用户名</label>
                    <input type="text" id="username" class="form-control" placeholder="请输入用户名" required>
                </div>
                <div class="form-group">
                    <label for="password" class="form-label">密码</label>
                    <input type="password" id="password" class="form-control" placeholder="请输入密码" required>
                </div>
                <div class="form-group" style="display: flex; justify-content: space-between; align-items: center;">
                    <label style="display: flex; align-items: center;">
                        <input type="checkbox" style="margin-right: 5px;"> 记住我
                    </label>
                    <a href="#" style="color: #1890ff; text-decoration: none;">忘记密码?</a>
                </div>
                <div class="form-group">
                    <button type="button" class="btn btn-primary" style="width: 100%;" onclick="login()">登录</button>
                </div>
            </form>
        </div>
    </div>

    <script>
        function login() {
            // In a real application, this would validate credentials with a backend
            // For this prototype, we'll just redirect to the dashboard
            window.location.href = 'dashboard.html';
        }
    </script>
</body>
</html>
