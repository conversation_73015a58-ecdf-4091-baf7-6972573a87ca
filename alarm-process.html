<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>精准曝气控制系统 (GPAC) - 告警处理</title>
    <link rel="stylesheet" href="css/all.min.css">
    <link rel="stylesheet" href="css/style.css">
    <style>
        /* 告警处理页面样式 */
        .process-container {
            padding: 24px;
        }

        .process-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 24px;
        }

        .process-header h3 {
            margin: 0;
            display: flex;
            align-items: center;
            gap: 10px;
        }

        .process-header .badge {
            margin-left: 12px;
        }

        .process-card {
            background: white;
            border-radius: 12px;
            box-shadow: 0 4px 12px rgba(0,0,0,0.08);
            padding: 24px;
            margin-bottom: 24px;
        }

        .alarm-summary {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 20px;
            margin-bottom: 24px;
        }

        .summary-item {
            display: flex;
            flex-direction: column;
        }

        .summary-label {
            font-size: 14px;
            color: var(--text-light);
            margin-bottom: 8px;
        }

        .summary-value {
            font-size: 16px;
            font-weight: 500;
        }

        .process-section {
            margin-bottom: 24px;
        }

        .process-section h4 {
            margin-top: 0;
            margin-bottom: 16px;
            padding-bottom: 8px;
            border-bottom: 1px solid var(--border-color);
        }

        .form-row {
            margin-bottom: 16px;
        }

        .form-actions {
            display: flex;
            justify-content: flex-end;
            gap: 12px;
            margin-top: 24px;
        }

        .timeline {
            position: relative;
            padding-left: 32px;
        }

        .timeline::before {
            content: '';
            position: absolute;
            left: 8px;
            top: 0;
            bottom: 0;
            width: 2px;
            background-color: var(--border-color);
        }

        .timeline-item {
            position: relative;
            margin-bottom: 24px;
        }

        .timeline-item:last-child {
            margin-bottom: 0;
        }

        .timeline-dot {
            position: absolute;
            left: -32px;
            top: 0;
            width: 16px;
            height: 16px;
            border-radius: 50%;
            background-color: var(--primary-color);
            border: 2px solid white;
            box-shadow: 0 0 0 2px var(--primary-color);
        }

        .timeline-content {
            background-color: #f9f9f9;
            border-radius: 8px;
            padding: 16px;
        }

        .timeline-time {
            font-size: 12px;
            color: var(--text-light);
            margin-bottom: 8px;
        }

        .timeline-title {
            font-weight: 500;
            margin-bottom: 8px;
        }

        .timeline-description {
            color: var(--text-light);
        }

        .tab-container {
            margin-bottom: 24px;
        }

        .tabs {
            display: flex;
            border-bottom: 1px solid var(--border-color);
            margin-bottom: 16px;
        }

        .tab {
            padding: 12px 24px;
            cursor: pointer;
            border-bottom: 2px solid transparent;
            transition: all 0.3s;
        }

        .tab.active {
            border-bottom-color: var(--primary-color);
            color: var(--primary-color);
            font-weight: 500;
        }

        .tab-content {
            display: none;
        }

        .tab-content.active {
            display: block;
        }
    </style>
</head>
<body>
    <div class="container">
        <!-- 顶部栏 -->
        <div class="topbar">
            <div class="topbar-left">
                <div class="toggle-sidebar" onclick="toggleSidebar()">
                    <i class="fas fa-bars"></i>
                </div>
                <img src="logo.png" alt="公司Logo" class="topbar-logo">
                <h2 class="topbar-title">精准曝气控制系统</h2>
            </div>
            <div class="topbar-right">
                <div class="user-dropdown">
                    <div class="user-info">
                        <div class="user-avatar">管</div>
                        <span>管理员</span>
                        <i class="fas fa-chevron-down" style="margin-left: 5px; font-size: 12px;"></i>
                    </div>
                    <div class="dropdown-content">
                        <a href="#"><i class="fas fa-user-circle"></i> 个人信息</a>
                        <a href="#"><i class="fas fa-cog"></i> 账号设置</a>
                        <a href="index.html" onclick="logout()"><i class="fas fa-sign-out-alt"></i> 退出登录</a>
                    </div>
                </div>
            </div>
        </div>

        <!-- 主体内容区域 -->
        <div class="main-container">
            <!-- 侧边栏 -->
            <div class="sidebar">
                <div class="sidebar-menu">
                    <div class="menu-item" id="menu-dashboard" onclick="window.location.href='dashboard.html'">
                        <i class="fas fa-tachometer-alt"></i>
                        <span>实时监控</span>
                    </div>
                    <div class="menu-item" id="menu-model" onclick="window.location.href='model-control.html'">
                        <i class="fas fa-brain"></i>
                        <span>模型控制</span>
                    </div>
                    <div class="menu-item" id="menu-scheduling" onclick="window.location.href='scheduling.html'">
                        <i class="fas fa-tasks"></i>
                        <span>调度管理</span>
                    </div>
                    <div class="menu-item" id="menu-reports" onclick="window.location.href='reports.html'">
                        <i class="fas fa-chart-bar"></i>
                        <span>数据报表</span>
                    </div>
                    <div class="menu-item active" id="menu-alarms" onclick="window.location.href='alarms.html'">
                        <i class="fas fa-bell"></i>
                        <span>告警管理</span>
                    </div>
                    <div class="menu-item" id="menu-settings" onclick="window.location.href='settings.html'">
                        <i class="fas fa-cog"></i>
                        <span>系统配置</span>
                    </div>
                </div>
            </div>

            <!-- 内容区域 -->
            <div class="content">

            <div class="process-container">
                <div class="process-header">
                    <h3>
                        <i class="fas fa-exclamation-triangle"></i>
                        <span id="alarm-title">告警处理</span>
                        <span id="alarm-badge" class="badge badge-critical">紧急</span>
                    </h3>
                    <div>
                        <button class="btn btn-secondary" onclick="window.location.href='alarms.html'">
                            <i class="fas fa-arrow-left"></i> 返回列表
                        </button>
                    </div>
                </div>

                <div class="process-card">
                    <div class="alarm-summary">
                        <div class="summary-item">
                            <div class="summary-label">告警ID</div>
                            <div class="summary-value" id="alarm-id">ALM-1234</div>
                        </div>
                        <div class="summary-item">
                            <div class="summary-label">告警时间</div>
                            <div class="summary-value" id="alarm-time">2025-05-16 10:23:45</div>
                        </div>
                        <div class="summary-item">
                            <div class="summary-label">告警级别</div>
                            <div class="summary-value" id="alarm-level">紧急</div>
                        </div>
                        <div class="summary-item">
                            <div class="summary-label">处理状态</div>
                            <div class="summary-value">处理中</div>
                        </div>
                    </div>

                    <div class="process-section">
                        <h4>告警描述</h4>
                        <p id="alarm-description">A区DO浓度低于阈值(1.2mg/L)，当前值：0.8mg/L</p>
                    </div>

                    <div class="tab-container">
                        <div class="tabs">
                            <div class="tab active" onclick="switchTab('tab-process')">处理操作</div>
                            <div class="tab" onclick="switchTab('tab-history')">处理历史</div>
                            <div class="tab" onclick="switchTab('tab-related')">相关告警</div>
                        </div>

                        <div id="tab-process" class="tab-content active">
                            <div class="process-section">
                                <h4>处理方案</h4>
                                <div class="form-row">
                                    <label class="form-label">处理类型</label>
                                    <select class="form-control">
                                        <option>现场处理</option>
                                        <option>远程调整</option>
                                        <option>设备重启</option>
                                        <option>参数校准</option>
                                        <option>其他</option>
                                    </select>
                                </div>
                                <div class="form-row">
                                    <label class="form-label">处理描述</label>
                                    <textarea class="form-control" rows="4" placeholder="请输入详细的处理步骤和结果..."></textarea>
                                </div>
                                <div class="form-row">
                                    <label class="form-label">处理结果</label>
                                    <select class="form-control">
                                        <option>已解决</option>
                                        <option>部分解决</option>
                                        <option>未解决</option>
                                        <option>需要进一步处理</option>
                                    </select>
                                </div>
                            </div>

                            <div class="form-actions">
                                <button class="btn btn-secondary">取消</button>
                                <button class="btn btn-primary" onclick="submitProcess()">
                                    <i class="fas fa-check"></i> 提交处理结果
                                </button>
                            </div>
                        </div>

                        <div id="tab-history" class="tab-content">
                            <div class="timeline">
                                <div class="timeline-item">
                                    <div class="timeline-dot"></div>
                                    <div class="timeline-content">
                                        <div class="timeline-time">2025-05-16 10:25:30</div>
                                        <div class="timeline-title">系统自动记录</div>
                                        <div class="timeline-description">告警触发，系统自动通知相关人员</div>
                                    </div>
                                </div>
                                <div class="timeline-item">
                                    <div class="timeline-dot"></div>
                                    <div class="timeline-content">
                                        <div class="timeline-time">2025-05-16 10:30:15</div>
                                        <div class="timeline-title">操作员确认</div>
                                        <div class="timeline-description">操作员已确认告警并开始处理</div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <div id="tab-related" class="tab-content">
                            <p>暂无相关告警</p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="js/common.js"></script>
    <script>
        // 初始化页面
        document.addEventListener('DOMContentLoaded', function() {
            // 加载公共元素
            loadCommonElements();

            // 从URL参数获取告警信息
            const urlParams = new URLSearchParams(window.location.search);

            // 设置告警信息
            if (urlParams.has('id')) {
                document.getElementById('alarm-id').textContent = urlParams.get('id');
            }

            if (urlParams.has('title')) {
                document.getElementById('alarm-title').textContent = urlParams.get('title');
            }

            if (urlParams.has('description')) {
                document.getElementById('alarm-description').textContent = urlParams.get('description');
            }

            if (urlParams.has('time')) {
                document.getElementById('alarm-time').textContent = urlParams.get('time');
            }

            if (urlParams.has('level')) {
                const level = urlParams.get('level');
                document.getElementById('alarm-level').textContent = level;

                // 设置徽章样式
                const badge = document.getElementById('alarm-badge');
                badge.textContent = level;

                if (level.includes('紧急')) {
                    badge.className = 'badge badge-critical';
                } else if (level.includes('警告')) {
                    badge.className = 'badge badge-warning';
                } else {
                    badge.className = 'badge badge-info';
                }
            }
        });

        // 切换标签页
        function switchTab(tabId) {
            // 隐藏所有标签内容
            const tabContents = document.querySelectorAll('.tab-content');
            tabContents.forEach(content => {
                content.classList.remove('active');
            });

            // 取消激活所有标签
            const tabs = document.querySelectorAll('.tab');
            tabs.forEach(tab => {
                tab.classList.remove('active');
            });

            // 激活选中的标签和内容
            document.getElementById(tabId).classList.add('active');
            const selectedTab = document.querySelector(`.tab[onclick="switchTab('${tabId}')"]`);
            selectedTab.classList.add('active');
        }

        // 提交处理结果
        function submitProcess() {
            showNotification('正在提交处理结果...', 'info');

            // 模拟提交过程
            setTimeout(() => {
                showNotification('告警处理结果已提交成功！', 'success');

                // 延迟返回告警列表
                setTimeout(() => {
                    window.location.href = 'alarms.html';
                }, 1500);
            }, 1000);
        }
    </script>
</body>
</html>
