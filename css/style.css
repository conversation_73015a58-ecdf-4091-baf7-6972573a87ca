/* GPAC - Global Styles */
:root {
  --primary-color: #1890ff;
  --secondary-color: #52c41a;
  --danger-color: #f5222d;
  --warning-color: #faad14;
  --text-color: #333333;
  --text-light: #666666;
  --border-color: #e8e8e8;
  --bg-color: #f0f2f5;
  --card-bg: #ffffff;
  --topbar-height: 70px;
  --header-height: 60px;
  --sidebar-width: 180px;
}

* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

body {
  font-family: "PingFang SC", "Helvetica Neue", Helvetica, "Hiragino Sans GB", "Microsoft YaHei", Arial, sans-serif;
  background-color: var(--bg-color);
  color: var(--text-color);
  line-height: 1.5;
}

.container {
  display: flex;
  flex-direction: column;
  min-height: 100vh;
}

/* 顶部栏样式 */
.topbar {
  height: var(--topbar-height);
  background-color: #fff;
  border-bottom: 1px solid var(--border-color);
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 0 20px;
  box-shadow: 0 1px 4px rgba(0,0,0,0.1);
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  z-index: 1001;
}

.topbar-left {
  display: flex;
  align-items: center;
}

.topbar-logo {
  height: 50px;
  margin-right: 15px;
  transition: transform 0.3s;
}

.topbar-logo:hover {
  transform: scale(1.05);
}

.topbar-title {
  font-size: 18px;
  font-weight: 500;
  color: var(--text-color);
}

.topbar-right {
  display: flex;
  align-items: center;
}

.user-info {
  display: flex;
  align-items: center;
  cursor: pointer;
}

.user-avatar {
  width: 32px;
  height: 32px;
  border-radius: 50%;
  background-color: var(--primary-color);
  color: white;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 8px;
  font-size: 14px;
}

/* 主体内容区域 */
.main-container {
  display: flex;
  margin-top: var(--topbar-height);
  min-height: calc(100vh - var(--topbar-height));
}

.sidebar {
  width: var(--sidebar-width);
  background-color: #001529;
  color: white;
  height: calc(100vh - var(--topbar-height));
  overflow-y: auto;
  transition: all 0.3s;
  z-index: 999;
  box-shadow: 2px 0 8px rgba(0,0,0,0.15);
  position: fixed;
  top: var(--topbar-height);
  left: 0;
}

/* 调整侧边栏头部布局，参考提供的设计 */
.sidebar-header {
  height: 60px;
  display: flex;
  flex-direction: row;
  align-items: center;
  padding: 0 15px;
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);
  background-color: #001529;
  position: relative;
}

.company-logo {
  width: 30px;
  height: 30px;
  margin-right: 10px;
  object-fit: contain;
  display: block;
  flex-shrink: 0;
}

.sidebar-header h2 {
  font-size: 15px;
  font-weight: 500;
  color: white;
  margin: 0;
  line-height: 1.2;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  max-width: 160px;
}

.sidebar-menu {
  padding: 16px 0;
}

.menu-item {
  padding: 12px 20px;
  display: flex;
  align-items: center;
  cursor: pointer;
  transition: all 0.3s;
  color: rgba(255, 255, 255, 0.65);
  border-left: 3px solid transparent;
}

.menu-item:hover, .menu-item.active {
  border-left: 3px solid var(--primary-color);
  background-color: rgba(24, 144, 255, 0.1);
  color: white;
}

.menu-item.active {
  background-color: rgba(24, 144, 255, 0.2);
}

.menu-item i {
  margin-right: 10px;
  font-size: 16px;
}

.main-content {
  flex: 1;
  transition: all 0.3s;
}

.header {
  height: var(--header-height);
  background-color: white;
  box-shadow: 0 1px 4px rgba(0, 21, 41, 0.08);
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 0 20px;
  position: sticky;
  top: 0;
  z-index: 100;
}

.header-left {
  display: flex;
  align-items: center;
}

.toggle-sidebar {
  font-size: 18px;
  margin-right: 15px;
  cursor: pointer;
  width: 32px;
  height: 32px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 4px;
  transition: all 0.2s;
}

.toggle-sidebar:hover {
  background-color: rgba(0, 0, 0, 0.05);
}

.header-logo {
  height: 24px;
  margin-right: 10px;
  object-fit: contain;
  vertical-align: middle;
}

.breadcrumb {
  font-size: 14px;
}

.header-right {
  display: flex;
  align-items: center;
}

.user-info {
  display: flex;
  align-items: center;
  cursor: pointer;
}

.user-avatar {
  width: 32px;
  height: 32px;
  border-radius: 50%;
  margin-right: 8px;
}

.content {
  padding: 20px;
  flex: 1;
  background-color: var(--bg-color);
  min-height: calc(100vh - var(--topbar-height));
  margin-left: var(--sidebar-width);
  width: calc(100% - var(--sidebar-width));
}

.card {
  background-color: var(--card-bg);
  border-radius: 4px;
  box-shadow: 0 1px 2px rgba(0, 0, 0, 0.05);
  margin-bottom: 20px;
  padding: 20px;
}

.card-header {
  border-bottom: 1px solid var(--border-color);
  padding-bottom: 15px;
  margin-bottom: 15px;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.card-title {
  font-size: 16px;
  font-weight: 500;
}

.grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 20px;
}

.btn {
  padding: 8px 15px;
  border-radius: 4px;
  border: none;
  cursor: pointer;
  font-size: 14px;
  transition: all 0.3s;
}

.btn-primary {
  background-color: var(--primary-color);
  color: white;
}

.btn-primary:hover {
  background-color: #40a9ff;
}

.btn-success {
  background-color: var(--secondary-color);
  color: white;
}

.btn-success:hover {
  background-color: #73d13d;
}

.btn-danger {
  background-color: var(--danger-color);
  color: white;
}

.btn-danger:hover {
  background-color: #ff4d4f;
}

.form-group {
  margin-bottom: 15px;
}

.form-label {
  display: block;
  margin-bottom: 5px;
  font-weight: 500;
}

.form-control {
  width: 100%;
  padding: 10px 12px;
  border: 1px solid var(--border-color);
  border-radius: 4px;
  font-size: 14px;
  transition: all 0.3s;
}

.form-control:focus {
  box-shadow: 0 0 0 2px rgba(24, 144, 255, 0.2);
}

.table {
  width: 100%;
  border-collapse: collapse;
}

.table th, .table td {
  padding: 12px 15px;
  text-align: left;
  border-bottom: 1px solid var(--border-color);
}

.table th {
  background-color: #fafafa;
  font-weight: 500;
}

/* Login page specific styles */
.login-container {
  display: flex;
  justify-content: center;
  align-items: center;
  min-height: 100vh;
  background: linear-gradient(135deg, #1890ff 0%, #096dd9 100%);
  position: relative;
  overflow: hidden;
}

.login-container::before {
  content: '';
  position: absolute;
  top: -50%;
  left: -50%;
  width: 200%;
  height: 200%;
  background: radial-gradient(circle, rgba(255,255,255,0.1) 0%, rgba(255,255,255,0) 60%);
  animation: rotate 30s linear infinite;
}

.login-form {
  width: 420px;
  padding: 40px;
  background-color: white;
  border-radius: 12px;
  box-shadow: 0 15px 35px rgba(0, 0, 0, 0.2);
  animation: fadeIn 0.5s ease-in-out;
  position: relative;
  z-index: 10;
}

.login-header {
  display: flex;
  align-items: center;
  justify-content: center;
  margin-bottom: 30px;
}

.login-logo-img {
  width: 40px;
  height: 40px;
  object-fit: contain;
  margin-right: 12px;
}

.login-title {
  font-size: 20px;
  font-weight: 500;
  color: var(--text-color);
  margin: 0;
}

@keyframes rotate {
  0% {
    transform: rotate(0deg);
  }
  100% {
    transform: rotate(360deg);
  }
}



@keyframes fadeIn {
  from { opacity: 0; transform: translateY(20px); }
  to { opacity: 1; transform: translateY(0); }
}

/* Dashboard specific styles */
.stat-card {
  text-align: center;
  padding: 20px;
}

.stat-value {
  font-size: 24px;
  font-weight: 600;
  margin: 10px 0;
}

.stat-label {
  color: var(--text-light);
}

.chart-container {
  height: 300px;
  margin-top: 20px;
}

/* Improved notification styling */
.notification {
  border-left: 4px solid;
  padding: 16px;
  animation: slideIn 0.3s ease-out;
}

.notification.success {
  border-left-color: var(--secondary-color);
}

.notification.error {
  border-left-color: var(--danger-color);
}

.notification.info {
  border-left-color: var(--primary-color);
}

@keyframes slideIn {
  from { transform: translateX(calc(100% + 20px)); opacity: 0; }
  to { transform: translateX(0); opacity: 1; }
}

/* Responsive styles */
@media (max-width: 768px) {
  .sidebar {
    transform: translateX(-100%);
    position: fixed;
    z-index: 1000;
  }

  .sidebar.open {
    transform: translateX(0);
  }

  .content {
    margin-left: 0;
    width: 100%;
  }

  .content.sidebar-open {
    margin-left: var(--sidebar-width);
    width: calc(100% - var(--sidebar-width));
  }

  .topbar-title {
    max-width: 200px;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
  }

  .grid {
    grid-template-columns: 1fr;
  }
}

/* User dropdown menu */
.user-dropdown {
  position: relative;
  display: inline-block;
}

.dropdown-content {
  display: none;
  position: absolute;
  right: 0;
  background-color: var(--card-bg);
  min-width: 160px;
  box-shadow: 0 8px 16px rgba(0,0,0,0.1);
  z-index: 1001;
  border-radius: 4px;
}

.dropdown-content a {
  color: var(--text-color);
  padding: 12px 16px;
  text-decoration: none;
  display: block;
  transition: all 0.2s;
}

.dropdown-content a:hover {
  background-color: var(--bg-color);
}

.user-dropdown:hover .dropdown-content {
  display: block;
}

/* Improved UI elements */
.btn {
  padding: 8px 16px;
  border-radius: 4px;
  font-weight: 500;
  transition: all 0.2s ease;
  box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.card {
  transition: transform 0.2s;
  border-radius: 8px;
}

.card:hover {
  transform: translateY(-3px);
  box-shadow: 0 4px 12px rgba(0,0,0,0.1);
}
