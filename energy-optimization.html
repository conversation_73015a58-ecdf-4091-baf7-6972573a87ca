<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>精准曝气控制系统 (GPAC) - 能源优化调度</title>
    <link rel="stylesheet" href="css/all.min.css">
    <link rel="stylesheet" href="css/style.css">
    <style>
        /* 子菜单样式 */
        .submenu {
            display: flex;
            flex-wrap: wrap;
            gap: 10px;
            margin-bottom: 20px;
            background-color: #fff;
            padding: 15px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);
        }

        .submenu-item {
            padding: 8px 16px;
            border-radius: 6px;
            cursor: pointer;
            transition: all 0.3s;
            display: flex;
            align-items: center;
            gap: 8px;
            font-size: 14px;
            color: #666;
        }

        .submenu-item:hover {
            background-color: #f5f7fa;
            color: var(--primary-color);
        }

        .submenu-item.active {
            background-color: var(--primary-color);
            color: white;
        }

        /* 标签页样式 */
        .tab-container {
            background-color: #fff;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);
            margin-bottom: 20px;
            overflow: hidden;
        }

        .tab-header {
            display: flex;
            border-bottom: 1px solid #f0f0f0;
            background-color: #fafafa;
        }

        .tab-item {
            padding: 15px 20px;
            cursor: pointer;
            font-weight: 500;
            color: #666;
            transition: all 0.3s;
            position: relative;
        }

        .tab-item:hover {
            color: var(--primary-color);
        }

        .tab-item.active {
            color: var(--primary-color);
        }

        .tab-item.active::after {
            content: '';
            position: absolute;
            bottom: 0;
            left: 0;
            width: 100%;
            height: 2px;
            background-color: var(--primary-color);
        }

        .tab-content {
            padding: 20px;
            display: none;
        }

        .tab-content.active {
            display: block;
        }

        /* 表单样式 */
        .form-section {
            margin-bottom: 25px;
            padding-bottom: 20px;
            border-bottom: 1px solid #f0f0f0;
        }

        .form-section:last-child {
            border-bottom: none;
            margin-bottom: 0;
            padding-bottom: 0;
        }

        .section-title {
            font-size: 16px;
            font-weight: 600;
            margin-bottom: 15px;
            color: #333;
            display: flex;
            align-items: center;
            gap: 8px;
        }

        .form-row {
            display: flex;
            flex-wrap: wrap;
            gap: 20px;
            margin-bottom: 15px;
        }

        .form-group {
            flex: 1;
            min-width: 250px;
        }

        .form-label {
            display: block;
            margin-bottom: 8px;
            font-size: 14px;
            color: #666;
        }

        .form-control {
            width: 100%;
            padding: 10px 12px;
            border: 1px solid #d9d9d9;
            border-radius: 4px;
            font-size: 14px;
            transition: all 0.3s;
        }

        .form-control:hover {
            border-color: #40a9ff;
        }

        .form-control:focus {
            border-color: #1890ff;
            box-shadow: 0 0 0 2px rgba(24, 144, 255, 0.2);
            outline: none;
        }

        .form-text {
            font-size: 12px;
            color: #999;
            margin-top: 5px;
        }

        /* 时间选择器样式 */
        .time-picker {
            display: flex;
            align-items: center;
            gap: 10px;
        }

        .time-picker .form-control {
            width: auto;
        }

        /* 开关样式 */
        .switch {
            position: relative;
            display: inline-block;
            width: 44px;
            height: 22px;
        }

        .switch input {
            opacity: 0;
            width: 0;
            height: 0;
        }

        .slider {
            position: absolute;
            cursor: pointer;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background-color: #ccc;
            transition: .4s;
            border-radius: 22px;
        }

        .slider:before {
            position: absolute;
            content: "";
            height: 18px;
            width: 18px;
            left: 3px;
            bottom: 2px;
            background-color: white;
            transition: .4s;
            border-radius: 50%;
        }

        input:checked + .slider {
            background-color: var(--primary-color);
        }

        input:focus + .slider {
            box-shadow: 0 0 1px var(--primary-color);
        }

        input:checked + .slider:before {
            transform: translateX(20px);
        }

        /* 表格样式 */
        .table-container {
            overflow-x: auto;
            margin-bottom: 20px;
        }

        .data-table {
            width: 100%;
            border-collapse: collapse;
        }

        .data-table th {
            background-color: #fafafa;
            padding: 12px 15px;
            text-align: left;
            font-weight: 500;
            color: #333;
            border-bottom: 1px solid #f0f0f0;
        }

        .data-table td {
            padding: 12px 15px;
            border-bottom: 1px solid #f0f0f0;
        }

        .data-table tr:hover {
            background-color: #f5f7fa;
        }

        /* 按钮样式 */
        .btn-group {
            display: flex;
            gap: 10px;
            margin-top: 20px;
            justify-content: flex-end;
        }

        .btn {
            padding: 8px 16px;
            border-radius: 4px;
            font-size: 14px;
            font-weight: 500;
            cursor: pointer;
            transition: all 0.3s;
            border: none;
        }

        .btn-primary {
            background-color: var(--primary-color);
            color: white;
        }

        .btn-primary:hover {
            background-color: #40a9ff;
            box-shadow: 0 2px 8px rgba(24, 144, 255, 0.3);
        }

        .btn-secondary {
            background-color: #f0f0f0;
            color: #666;
        }

        .btn-secondary:hover {
            background-color: #e0e0e0;
        }

        .btn-danger {
            background-color: #ff4d4f;
            color: white;
        }

        .btn-danger:hover {
            background-color: #ff7875;
            box-shadow: 0 2px 8px rgba(255, 77, 79, 0.3);
        }

        /* 图表容器样式 */
        .chart-container {
            height: 300px;
            margin-bottom: 20px;
            background-color: #fff;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);
            padding: 15px;
        }

        /* 卡片样式 */
        .card {
            background-color: #fff;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);
            padding: 15px;
            margin-bottom: 20px;
        }

        .card-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 15px;
            padding-bottom: 10px;
            border-bottom: 1px solid #f0f0f0;
        }

        .card-title {
            font-size: 16px;
            font-weight: 600;
            margin: 0;
            color: #333;
            display: flex;
            align-items: center;
            gap: 8px;
        }

        /* 进度条样式 */
        .progress-container {
            width: 100%;
            background-color: #f0f0f0;
            border-radius: 4px;
            margin: 10px 0;
        }

        .progress-bar {
            height: 8px;
            border-radius: 4px;
            background-color: var(--primary-color);
        }

        .progress-bar.warning {
            background-color: #faad14;
        }

        .progress-bar.danger {
            background-color: #ff4d4f;
        }

        /* 标签样式 */
        .tag {
            display: inline-block;
            padding: 2px 8px;
            border-radius: 4px;
            font-size: 12px;
            margin-right: 5px;
        }

        .tag-primary {
            background-color: rgba(24, 144, 255, 0.1);
            color: var(--primary-color);
            border: 1px solid rgba(24, 144, 255, 0.2);
        }

        .tag-success {
            background-color: rgba(82, 196, 26, 0.1);
            color: #52c41a;
            border: 1px solid rgba(82, 196, 26, 0.2);
        }

        .tag-warning {
            background-color: rgba(250, 173, 20, 0.1);
            color: #faad14;
            border: 1px solid rgba(250, 173, 20, 0.2);
        }

        .tag-danger {
            background-color: rgba(245, 34, 45, 0.1);
            color: #f5222d;
            border: 1px solid rgba(245, 34, 45, 0.2);
        }

        /* 响应式调整 */
        @media (max-width: 768px) {
            .form-row {
                flex-direction: column;
                gap: 15px;
            }

            .form-group {
                min-width: 100%;
            }

            .submenu {
                overflow-x: auto;
                padding: 10px;
                flex-wrap: nowrap;
            }

            .submenu-item {
                white-space: nowrap;
            }
        }
    </style>
</head>
<body>
<script>document.addEventListener("DOMContentLoaded", function() { loadCommonElements(); });</script>
    <div class="container">
        <!-- 顶部栏 -->
        <div class="topbar">
            <div class="topbar-left">
                <div class="toggle-sidebar" onclick="toggleSidebar()">
                    <i class="fas fa-bars"></i>
                </div>
                <img src="logo.png" alt="公司Logo" class="topbar-logo" style="height: 60px; margin-right: 15px; transition: transform 0.3s;" onmouseover="this.style.transform='scale(1.05)'" onmouseout="this.style.transform='scale(1)'">
                <h2 class="topbar-title">精准曝气控制系统</h2>
            </div>
            <div class="topbar-right">
                <div class="user-dropdown">
                    <div class="user-info">
                        <div class="user-avatar">管</div>
                        <span>管理员</span>
                        <i class="fas fa-chevron-down" style="margin-left: 5px; font-size: 12px;"></i>
                    </div>
                    <div class="dropdown-content">
                        <a href="#"><i class="fas fa-user-circle"></i> 个人信息</a>
                        <a href="#"><i class="fas fa-cog"></i> 账号设置</a>
                        <a href="index.html" onclick="logout()"><i class="fas fa-sign-out-alt"></i> 退出登录</a>
                    </div>
                </div>
            </div>
        </div>

        <!-- 主体内容区域 -->
        <div class="main-container">
            <!-- 侧边栏 -->
            <div class="sidebar">
                <div class="sidebar-menu">
                    <div class="menu-item" id="menu-dashboard" onclick="window.location.href='dashboard.html'">
                        <i class="fas fa-tachometer-alt"></i>
                        <span>实时监控</span>
                    </div>
                    <div class="menu-item" id="menu-model" onclick="window.location.href='model-control.html'">
                        <i class="fas fa-brain"></i>
                        <span>模型控制</span>
                    </div>
                    <div class="menu-item active" id="menu-scheduling" onclick="window.location.href='scheduling.html'">
                        <i class="fas fa-tasks"></i>
                        <span>调度管理</span>
                    </div>
                    <div class="menu-item" id="menu-reports" onclick="window.location.href='reports.html'">
                        <i class="fas fa-chart-bar"></i>
                        <span>数据报表</span>
                    </div>
                    <div class="menu-item" id="menu-alarms" onclick="window.location.href='alarms.html'">
                        <i class="fas fa-bell"></i>
                        <span>告警管理</span>
                    </div>
                    <div class="menu-item" id="menu-settings" onclick="window.location.href='settings.html'">
                        <i class="fas fa-cog"></i>
                        <span>系统配置</span>
                    </div>
                </div>
            </div>

            <!-- 内容区域 -->
            <div class="content">
                <!-- 子菜单导航 -->
                <div class="submenu">
                    <div class="submenu-item" onclick="window.location.href='scheduling.html'">
                        <i class="fas fa-home"></i> 调度总览
                    </div>
                    <div class="submenu-item" onclick="window.location.href='device-scheduling.html'">
                        <i class="fas fa-fan"></i> 曝气设备调度
                    </div>
                    <div class="submenu-item" onclick="window.location.href='do-parameter-scheduling.html'">
                        <i class="fas fa-sliders-h"></i> DO控制参数调度
                    </div>
                    <div class="submenu-item active" onclick="window.location.href='energy-optimization.html'">
                        <i class="fas fa-bolt"></i> 能源优化调度
                    </div>
                    <div class="submenu-item" onclick="window.location.href='plan-management.html'">
                        <i class="fas fa-calendar-alt"></i> 调度计划管理
                    </div>
                    <div class="submenu-item" onclick="window.location.href='execution-monitoring.html'">
                        <i class="fas fa-desktop"></i> 执行监控
                    </div>
                    <div class="submenu-item" onclick="window.location.href='statistics-reports.html'">
                        <i class="fas fa-chart-pie"></i> 统计报表
                    </div>
                </div>

                <!-- 页面标题 -->
                <div class="page-header">
                    <h1 class="page-title">
                        <i class="fas fa-bolt" style="color: var(--primary-color);"></i>
                        能源优化调度
                    </h1>
                    <div class="page-actions">
                        <button class="btn btn-primary" onclick="saveAllSettings()">
                            <i class="fas fa-save"></i> 保存所有设置
                        </button>
                    </div>
                </div>

                <!-- 标签页容器 -->
                <div class="tab-container">
                    <div class="tab-header">
                        <div class="tab-item active" data-tab="peak-valley">
                            <i class="fas fa-chart-line"></i> 峰谷电价响应
                        </div>
                        <div class="tab-item" data-tab="min-energy">
                            <i class="fas fa-leaf"></i> 最小能耗运行
                        </div>
                        <div class="tab-item" data-tab="budget-control">
                            <i class="fas fa-wallet"></i> 能耗预算控制
                        </div>
                    </div>

                    <!-- 峰谷电价响应 -->
                    <div class="tab-content active" id="peak-valley">
                        <!-- 电价时段设置 -->
                        <div class="form-section">
                            <h3 class="section-title">
                                <i class="fas fa-clock" style="color: #1890ff;"></i>
                                电价时段设置
                            </h3>
                            <div class="form-row">
                                <div class="form-group">
                                    <label class="form-label">电价方案</label>
                                    <select class="form-control" id="price-scheme">
                                        <option value="standard" selected>标准峰平谷方案</option>
                                        <option value="summer">夏季峰平谷方案</option>
                                        <option value="winter">冬季峰平谷方案</option>
                                        <option value="custom">自定义方案</option>
                                    </select>
                                </div>
                                <div class="form-group">
                                    <label class="form-label">方案状态</label>
                                    <div style="display: flex; align-items: center; gap: 10px;">
                                        <label class="switch">
                                            <input type="checkbox" checked>
                                            <span class="slider"></span>
                                        </label>
                                        <span>启用峰谷电价响应</span>
                                    </div>
                                </div>
                            </div>

                            <div class="table-container">
                                <table class="data-table">
                                    <thead>
                                        <tr>
                                            <th>时段类型</th>
                                            <th>时间范围</th>
                                            <th>电价 (元/kWh)</th>
                                            <th>颜色标识</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <tr>
                                            <td>峰时段</td>
                                            <td>
                                                <div style="display: flex; flex-direction: column; gap: 5px;">
                                                    <div class="time-picker">
                                                        <input type="time" class="form-control" value="08:00">
                                                        <span>至</span>
                                                        <input type="time" class="form-control" value="11:00">
                                                    </div>
                                                    <div class="time-picker">
                                                        <input type="time" class="form-control" value="18:00">
                                                        <span>至</span>
                                                        <input type="time" class="form-control" value="21:00">
                                                    </div>
                                                </div>
                                            </td>
                                            <td>
                                                <input type="number" class="form-control" value="1.2" min="0" step="0.01" style="width: 80px;">
                                            </td>
                                            <td>
                                                <div style="width: 20px; height: 20px; background-color: #f5222d; border-radius: 4px;"></div>
                                            </td>
                                        </tr>
                                        <tr>
                                            <td>平时段</td>
                                            <td>
                                                <div style="display: flex; flex-direction: column; gap: 5px;">
                                                    <div class="time-picker">
                                                        <input type="time" class="form-control" value="11:00">
                                                        <span>至</span>
                                                        <input type="time" class="form-control" value="18:00">
                                                    </div>
                                                    <div class="time-picker">
                                                        <input type="time" class="form-control" value="21:00">
                                                        <span>至</span>
                                                        <input type="time" class="form-control" value="23:00">
                                                    </div>
                                                </div>
                                            </td>
                                            <td>
                                                <input type="number" class="form-control" value="0.8" min="0" step="0.01" style="width: 80px;">
                                            </td>
                                            <td>
                                                <div style="width: 20px; height: 20px; background-color: #faad14; border-radius: 4px;"></div>
                                            </td>
                                        </tr>
                                        <tr>
                                            <td>谷时段</td>
                                            <td>
                                                <div style="display: flex; flex-direction: column; gap: 5px;">
                                                    <div class="time-picker">
                                                        <input type="time" class="form-control" value="23:00">
                                                        <span>至</span>
                                                        <input type="time" class="form-control" value="08:00">
                                                    </div>
                                                </div>
                                            </td>
                                            <td>
                                                <input type="number" class="form-control" value="0.4" min="0" step="0.01" style="width: 80px;">
                                            </td>
                                            <td>
                                                <div style="width: 20px; height: 20px; background-color: #52c41a; border-radius: 4px;"></div>
                                            </td>
                                        </tr>
                                    </tbody>
                                </table>
                            </div>

                            <div class="form-row" style="margin-top: 15px;">
                                <div class="form-group">
                                    <button class="btn btn-secondary" onclick="addPriceTimeSlot()">
                                        <i class="fas fa-plus"></i> 添加时段
                                    </button>
                                </div>
                            </div>
                        </div>

                        <!-- 响应策略 -->
                        <div class="form-section">
                            <h3 class="section-title">
                                <i class="fas fa-cogs" style="color: #52c41a;"></i>
                                响应策略
                            </h3>
                            <div class="table-container">
                                <table class="data-table">
                                    <thead>
                                        <tr>
                                            <th>时段类型</th>
                                            <th>DO目标值调整</th>
                                            <th>曝气设备运行策略</th>
                                            <th>优先级</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <tr>
                                            <td>峰时段</td>
                                            <td>
                                                <select class="form-control">
                                                    <option>降低10%</option>
                                                    <option selected>降低5%</option>
                                                    <option>不调整</option>
                                                    <option>自定义</option>
                                                </select>
                                            </td>
                                            <td>
                                                <select class="form-control">
                                                    <option>最小功率运行</option>
                                                    <option selected>节能模式运行</option>
                                                    <option>标准模式运行</option>
                                                    <option>自定义</option>
                                                </select>
                                            </td>
                                            <td>
                                                <select class="form-control">
                                                    <option>高</option>
                                                    <option selected>中</option>
                                                    <option>低</option>
                                                </select>
                                            </td>
                                        </tr>
                                        <tr>
                                            <td>平时段</td>
                                            <td>
                                                <select class="form-control">
                                                    <option>降低5%</option>
                                                    <option selected>不调整</option>
                                                    <option>提高5%</option>
                                                    <option>自定义</option>
                                                </select>
                                            </td>
                                            <td>
                                                <select class="form-control">
                                                    <option>节能模式运行</option>
                                                    <option selected>标准模式运行</option>
                                                    <option>高效模式运行</option>
                                                    <option>自定义</option>
                                                </select>
                                            </td>
                                            <td>
                                                <select class="form-control">
                                                    <option>高</option>
                                                    <option selected>中</option>
                                                    <option>低</option>
                                                </select>
                                            </td>
                                        </tr>
                                        <tr>
                                            <td>谷时段</td>
                                            <td>
                                                <select class="form-control">
                                                    <option>不调整</option>
                                                    <option>提高5%</option>
                                                    <option selected>提高10%</option>
                                                    <option>自定义</option>
                                                </select>
                                            </td>
                                            <td>
                                                <select class="form-control">
                                                    <option>标准模式运行</option>
                                                    <option selected>高效模式运行</option>
                                                    <option>最大功率运行</option>
                                                    <option>自定义</option>
                                                </select>
                                            </td>
                                            <td>
                                                <select class="form-control">
                                                    <option selected>高</option>
                                                    <option>中</option>
                                                    <option>低</option>
                                                </select>
                                            </td>
                                        </tr>
                                    </tbody>
                                </table>
                            </div>
                        </div>

                        <!-- 预测响应 -->
                        <div class="form-section">
                            <h3 class="section-title">
                                <i class="fas fa-chart-area" style="color: #722ed1;"></i>
                                预测响应
                            </h3>
                            <div class="form-row">
                                <div class="form-group">
                                    <label class="form-label">预测响应状态</label>
                                    <div style="display: flex; align-items: center; gap: 10px;">
                                        <label class="switch">
                                            <input type="checkbox" checked>
                                            <span class="slider"></span>
                                        </label>
                                        <span>启用电价预测响应</span>
                                    </div>
                                </div>
                                <div class="form-group">
                                    <label class="form-label">预测提前时间</label>
                                    <select class="form-control">
                                        <option>30分钟</option>
                                        <option selected>1小时</option>
                                        <option>2小时</option>
                                        <option>4小时</option>
                                    </select>
                                </div>
                            </div>

                            <div class="form-row">
                                <div class="form-group">
                                    <label class="form-label">预测数据来源</label>
                                    <select class="form-control">
                                        <option selected>电力公司API</option>
                                        <option>历史数据预测</option>
                                        <option>手动设置</option>
                                    </select>
                                </div>
                                <div class="form-group">
                                    <label class="form-label">预测准确度阈值</label>
                                    <input type="number" class="form-control" value="85" min="0" max="100">
                                    <div class="form-text">预测准确度低于此阈值时将不执行预测响应</div>
                                </div>
                            </div>

                            <div class="chart-container">
                                <div style="text-align: center; padding: 20px;">
                                    <i class="fas fa-chart-line" style="font-size: 48px; color: #d9d9d9;"></i>
                                    <p>电价预测图表将在此显示</p>
                                </div>
                            </div>
                        </div>

                        <!-- 电价数据导入 -->
                        <div class="form-section">
                            <h3 class="section-title">
                                <i class="fas fa-file-import" style="color: #fa8c16;"></i>
                                电价数据导入
                            </h3>
                            <div class="form-row">
                                <div class="form-group">
                                    <label class="form-label">数据导入方式</label>
                                    <select class="form-control" id="import-method">
                                        <option value="file">文件导入</option>
                                        <option value="api" selected>API接入</option>
                                        <option value="manual">手动输入</option>
                                    </select>
                                </div>
                            </div>

                            <div id="file-import">
                                <div class="form-row">
                                    <div class="form-group">
                                        <label class="form-label">选择文件</label>
                                        <div style="display: flex; gap: 10px;">
                                            <input type="file" class="form-control">
                                            <button class="btn btn-secondary">
                                                <i class="fas fa-upload"></i> 上传
                                            </button>
                                        </div>
                                        <div class="form-text">支持Excel、CSV格式</div>
                                    </div>
                                </div>
                            </div>

                            <div id="api-import" style="display: block;">
                                <div class="form-row">
                                    <div class="form-group">
                                        <label class="form-label">API地址</label>
                                        <input type="text" class="form-control" value="https://api.electric-company.com/price-data">
                                    </div>
                                    <div class="form-group">
                                        <label class="form-label">API密钥</label>
                                        <input type="password" class="form-control" value="********">
                                    </div>
                                </div>
                                <div class="form-row">
                                    <div class="form-group">
                                        <label class="form-label">数据更新频率</label>
                                        <select class="form-control">
                                            <option>每小时</option>
                                            <option selected>每天</option>
                                            <option>每周</option>
                                            <option>手动更新</option>
                                        </select>
                                    </div>
                                    <div class="form-group">
                                        <label class="form-label">上次更新时间</label>
                                        <input type="text" class="form-control" value="2025-05-10 08:30:15" disabled>
                                    </div>
                                </div>
                                <div class="form-row">
                                    <div class="form-group">
                                        <button class="btn btn-primary">
                                            <i class="fas fa-sync-alt"></i> 立即更新数据
                                        </button>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <div class="btn-group">
                            <button class="btn btn-secondary">取消</button>
                            <button class="btn btn-primary" onclick="savePeakValleySettings()">保存设置</button>
                        </div>
                    </div>

                    <!-- 最小能耗运行标签页 -->
                    <div class="tab-content" id="min-energy">
                        <!-- 能耗目标设置 -->
                        <div class="form-section">
                            <h3 class="section-title">
                                <i class="fas fa-bullseye" style="color: #1890ff;"></i>
                                能耗目标设置
                            </h3>
                            <div class="form-row">
                                <div class="form-group">
                                    <label class="form-label">目标类型</label>
                                    <select class="form-control" id="energy-target-type">
                                        <option value="daily" selected>每日能耗目标</option>
                                        <option value="weekly">每周能耗目标</option>
                                        <option value="monthly">每月能耗目标</option>
                                    </select>
                                </div>
                                <div class="form-group">
                                    <label class="form-label">目标状态</label>
                                    <div style="display: flex; align-items: center; gap: 10px;">
                                        <label class="switch">
                                            <input type="checkbox" checked>
                                            <span class="slider"></span>
                                        </label>
                                        <span>启用能耗目标控制</span>
                                    </div>
                                </div>
                            </div>

                            <div class="form-row">
                                <div class="form-group">
                                    <label class="form-label">每日能耗目标 (kWh)</label>
                                    <input type="number" class="form-control" value="1200" min="0">
                                </div>
                                <div class="form-group">
                                    <label class="form-label">每月能耗目标 (kWh)</label>
                                    <input type="number" class="form-control" value="36000" min="0">
                                </div>
                            </div>

                            <div class="form-row">
                                <div class="form-group">
                                    <label class="form-label">目标调整策略</label>
                                    <select class="form-control">
                                        <option>固定目标</option>
                                        <option selected>根据历史数据自动调整</option>
                                        <option>根据水量自动调整</option>
                                        <option>根据季节自动调整</option>
                                    </select>
                                </div>
                                <div class="form-group">
                                    <label class="form-label">调整周期</label>
                                    <select class="form-control">
                                        <option>每周</option>
                                        <option selected>每月</option>
                                        <option>每季度</option>
                                    </select>
                                </div>
                            </div>

                            <div class="chart-container">
                                <div style="text-align: center; padding: 20px;">
                                    <i class="fas fa-chart-bar" style="font-size: 48px; color: #d9d9d9;"></i>
                                    <p>能耗目标与实际能耗对比图表将在此显示</p>
                                </div>
                            </div>
                        </div>

                        <!-- 智能调节 -->
                        <div class="form-section">
                            <h3 class="section-title">
                                <i class="fas fa-brain" style="color: #52c41a;"></i>
                                智能调节
                            </h3>
                            <div class="form-row">
                                <div class="form-group">
                                    <label class="form-label">智能调节状态</label>
                                    <div style="display: flex; align-items: center; gap: 10px;">
                                        <label class="switch">
                                            <input type="checkbox" checked>
                                            <span class="slider"></span>
                                        </label>
                                        <span>启用智能能耗调节</span>
                                    </div>
                                </div>
                                <div class="form-group">
                                    <label class="form-label">调节模式</label>
                                    <select class="form-control">
                                        <option>保守模式</option>
                                        <option selected>平衡模式</option>
                                        <option>激进模式</option>
                                    </select>
                                </div>
                            </div>

                            <div class="form-row">
                                <div class="form-group">
                                    <label class="form-label">调节参数</label>
                                    <div class="table-container">
                                        <table class="data-table">
                                            <thead>
                                                <tr>
                                                    <th>参数名称</th>
                                                    <th>当前值</th>
                                                    <th>调节范围</th>
                                                    <th>优先级</th>
                                                </tr>
                                            </thead>
                                            <tbody>
                                                <tr>
                                                    <td>DO目标值</td>
                                                    <td>
                                                        <input type="number" class="form-control" value="2.0" min="0" max="10" step="0.1" style="width: 80px;">
                                                    </td>
                                                    <td>1.5 - 3.0 mg/L</td>
                                                    <td>
                                                        <select class="form-control">
                                                            <option selected>高</option>
                                                            <option>中</option>
                                                            <option>低</option>
                                                        </select>
                                                    </td>
                                                </tr>
                                                <tr>
                                                    <td>鼓风机运行数量</td>
                                                    <td>
                                                        <input type="number" class="form-control" value="2" min="1" max="5" style="width: 80px;">
                                                    </td>
                                                    <td>1 - 3 台</td>
                                                    <td>
                                                        <select class="form-control">
                                                            <option>高</option>
                                                            <option selected>中</option>
                                                            <option>低</option>
                                                        </select>
                                                    </td>
                                                </tr>
                                                <tr>
                                                    <td>曝气时间比例</td>
                                                    <td>
                                                        <input type="number" class="form-control" value="80" min="0" max="100" style="width: 80px;">
                                                    </td>
                                                    <td>60% - 100%</td>
                                                    <td>
                                                        <select class="form-control">
                                                            <option>高</option>
                                                            <option>中</option>
                                                            <option selected>低</option>
                                                        </select>
                                                    </td>
                                                </tr>
                                            </tbody>
                                        </table>
                                    </div>
                                </div>
                            </div>

                            <div class="form-row">
                                <div class="form-group">
                                    <label class="form-label">调节限制条件</label>
                                    <div style="margin-bottom: 10px;">
                                        <div style="display: flex; align-items: center; gap: 10px; margin-bottom: 10px;">
                                            <input type="checkbox" checked id="limit-condition-1">
                                            <label for="limit-condition-1">出水水质不得低于标准</label>
                                        </div>
                                        <div style="display: flex; align-items: center; gap: 10px; margin-bottom: 10px;">
                                            <input type="checkbox" checked id="limit-condition-2">
                                            <label for="limit-condition-2">池内DO不得低于</label>
                                            <input type="number" class="form-control" style="width: 80px;" value="0.5">
                                            <span>mg/L</span>
                                        </div>
                                        <div style="display: flex; align-items: center; gap: 10px;">
                                            <input type="checkbox" checked id="limit-condition-3">
                                            <label for="limit-condition-3">设备启停次数不超过</label>
                                            <input type="number" class="form-control" style="width: 80px;" value="6">
                                            <span>次/天</span>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- 能耗预警 -->
                        <div class="form-section">
                            <h3 class="section-title">
                                <i class="fas fa-exclamation-triangle" style="color: #fa8c16;"></i>
                                能耗预警
                            </h3>
                            <div class="form-row">
                                <div class="form-group">
                                    <label class="form-label">预警状态</label>
                                    <div style="display: flex; align-items: center; gap: 10px;">
                                        <label class="switch">
                                            <input type="checkbox" checked>
                                            <span class="slider"></span>
                                        </label>
                                        <span>启用能耗预警</span>
                                    </div>
                                </div>
                                <div class="form-group">
                                    <label class="form-label">预警级别</label>
                                    <div style="display: flex; flex-direction: column; gap: 10px;">
                                        <div style="display: flex; align-items: center; gap: 10px;">
                                            <span>一级预警（黄色）：</span>
                                            <input type="number" class="form-control" value="85" min="0" max="100" style="width: 80px;">
                                            <span>% 目标值</span>
                                        </div>
                                        <div style="display: flex; align-items: center; gap: 10px;">
                                            <span>二级预警（橙色）：</span>
                                            <input type="number" class="form-control" value="95" min="0" max="100" style="width: 80px;">
                                            <span>% 目标值</span>
                                        </div>
                                        <div style="display: flex; align-items: center; gap: 10px;">
                                            <span>三级预警（红色）：</span>
                                            <input type="number" class="form-control" value="105" min="0" max="150" style="width: 80px;">
                                            <span>% 目标值</span>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <div class="form-row">
                                <div class="form-group">
                                    <label class="form-label">预警通知方式</label>
                                    <div style="margin-bottom: 10px;">
                                        <div style="display: flex; align-items: center; gap: 10px; margin-bottom: 10px;">
                                            <input type="checkbox" checked id="notification-method-1">
                                            <label for="notification-method-1">系统内部通知</label>
                                        </div>
                                        <div style="display: flex; align-items: center; gap: 10px; margin-bottom: 10px;">
                                            <input type="checkbox" checked id="notification-method-2">
                                            <label for="notification-method-2">短信通知</label>
                                        </div>
                                        <div style="display: flex; align-items: center; gap: 10px;">
                                            <input type="checkbox" id="notification-method-3">
                                            <label for="notification-method-3">邮件通知</label>
                                        </div>
                                    </div>
                                </div>
                                <div class="form-group">
                                    <label class="form-label">预警响应措施</label>
                                    <select class="form-control" multiple style="height: 100px;">
                                        <option selected>自动降低DO目标值</option>
                                        <option selected>自动调整曝气设备运行模式</option>
                                        <option selected>通知管理人员</option>
                                        <option>自动切换到节能参数组</option>
                                        <option>暂停非必要设备运行</option>
                                    </select>
                                    <div class="form-text">按住Ctrl键可多选</div>
                                </div>
                            </div>
                        </div>

                        <!-- 节能建议 -->
                        <div class="form-section">
                            <h3 class="section-title">
                                <i class="fas fa-lightbulb" style="color: #722ed1;"></i>
                                节能建议
                            </h3>
                            <div class="card">
                                <div class="card-header">
                                    <h4 class="card-title">
                                        <i class="fas fa-star" style="color: #faad14;"></i>
                                        系统生成的节能运行建议
                                    </h4>
                                </div>
                                <div style="margin-bottom: 15px;">
                                    <div style="display: flex; align-items: center; gap: 10px; margin-bottom: 10px;">
                                        <i class="fas fa-check-circle" style="color: #52c41a;"></i>
                                        <span>建议在电价峰时段（08:00-11:00, 18:00-21:00）降低DO目标值至1.8 mg/L，预计可节省能耗8%</span>
                                    </div>
                                    <div style="display: flex; align-items: center; gap: 10px; margin-bottom: 10px;">
                                        <i class="fas fa-check-circle" style="color: #52c41a;"></i>
                                        <span>建议优化鼓风机启停策略，减少频繁启停，预计可节省能耗5%</span>
                                    </div>
                                    <div style="display: flex; align-items: center; gap: 10px; margin-bottom: 10px;">
                                        <i class="fas fa-check-circle" style="color: #52c41a;"></i>
                                        <span>建议在水温较高时段（12:00-16:00）适当降低曝气量，预计可节省能耗3%</span>
                                    </div>
                                    <div style="display: flex; align-items: center; gap: 10px;">
                                        <i class="fas fa-check-circle" style="color: #52c41a;"></i>
                                        <span>建议检查2号鼓风机效率，当前效率低于平均水平15%，可能需要维护</span>
                                    </div>
                                </div>
                                <div style="text-align: right;">
                                    <button class="btn btn-primary">
                                        <i class="fas fa-magic"></i> 应用所有建议
                                    </button>
                                    <button class="btn btn-secondary">
                                        <i class="fas fa-sync-alt"></i> 刷新建议
                                    </button>
                                </div>
                            </div>
                        </div>

                        <div class="btn-group">
                            <button class="btn btn-secondary">取消</button>
                            <button class="btn btn-primary" onclick="saveMinEnergySettings()">保存设置</button>
                        </div>
                    </div>

                    <!-- 能耗预算控制标签页 -->
                    <div class="tab-content" id="budget-control">
                        <!-- 内容将在下一步添加 -->
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="js/common.js"></script>
    <script>
        // 初始化公共元素
        document.addEventListener('DOMContentLoaded', function() {
            // 这里可以添加页面初始化代码
        });

        // 保存所有设置
        function saveAllSettings() {
            showNotification('正在保存所有设置...', 'info');

            // 模拟保存延迟
            setTimeout(function() {
                showNotification('所有设置已保存', 'success');
            }, 1500);
        }

        // 显示通知
        function showNotification(message, type) {
            // 假设common.js中已经实现了这个函数
            if (window.showNotification) {
                window.showNotification(message, type);
            } else {
                alert(message);
            }
        }
    </script>
</body>
</html>
