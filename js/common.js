// Common JavaScript functions for GPAC system

// Toggle sidebar visibility
function toggleSidebar() {
    const sidebar = document.querySelector('.sidebar');
    const content = document.querySelector('.content');

    sidebar.classList.toggle('open');

    // Also toggle the content class for responsive design
    if (window.innerWidth <= 768) {
        content.classList.toggle('sidebar-open');
    }
}

// Set active menu item
function setActiveMenuItem(id) {
    const menuItems = document.querySelectorAll('.menu-item');
    menuItems.forEach(item => {
        item.classList.remove('active');
    });

    const activeItem = document.getElementById(id);
    if (activeItem) {
        activeItem.classList.add('active');
    }
}

// Format date
function formatDate(date) {
    const d = new Date(date);
    const year = d.getFullYear();
    const month = String(d.getMonth() + 1).padStart(2, '0');
    const day = String(d.getDate()).padStart(2, '0');
    const hours = String(d.getHours()).padStart(2, '0');
    const minutes = String(d.getMinutes()).padStart(2, '0');

    return `${year}-${month}-${day} ${hours}:${minutes}`;
}

// Generate random data for charts (for prototype purposes)
function generateRandomData(count, min, max) {
    const data = [];
    for (let i = 0; i < count; i++) {
        data.push(Math.random() * (max - min) + min);
    }
    return data;
}

// Generate time labels for charts (for prototype purposes)
function generateTimeLabels(count) {
    const labels = [];
    const now = new Date();

    for (let i = count - 1; i >= 0; i--) {
        const time = new Date(now - i * 5 * 60000); // 5 minutes intervals
        const hours = String(time.getHours()).padStart(2, '0');
        const minutes = String(time.getMinutes()).padStart(2, '0');
        labels.push(`${hours}:${minutes}`);
    }

    return labels;
}

// Show notification
function showNotification(message, type = 'info') {
    const notification = document.createElement('div');
    notification.className = `notification ${type}`;
    notification.innerHTML = `
        <div class="notification-content">
            <i class="fas ${type === 'success' ? 'fa-check-circle' : type === 'error' ? 'fa-times-circle' : 'fa-info-circle'}"></i>
            <span>${message}</span>
        </div>
    `;

    document.body.appendChild(notification);

    // Show notification
    setTimeout(() => {
        notification.classList.add('show');
    }, 10);

    // Hide and remove notification after 3 seconds
    setTimeout(() => {
        notification.classList.remove('show');
        setTimeout(() => {
            document.body.removeChild(notification);
        }, 300);
    }, 3000);
}

// Load header and sidebar
function loadCommonElements() {
    // Get current page filename
    const path = window.location.pathname;
    const page = path.split("/").pop();

    // Set active menu item based on current page
    let activeMenuId = 'menu-dashboard';

    switch(page) {
        case 'dashboard.html':
            activeMenuId = 'menu-dashboard';
            break;
        case 'model-control.html':
            activeMenuId = 'menu-model';
            break;
        case 'scheduling.html':
            activeMenuId = 'menu-scheduling';
            break;
        case 'reports.html':
            activeMenuId = 'menu-reports';
            break;
        case 'alarms.html':
            activeMenuId = 'menu-alarms';
            break;
        case 'settings.html':
            activeMenuId = 'menu-settings';
            break;
    }

    // Set active menu item after DOM is fully loaded
    document.addEventListener('DOMContentLoaded', () => {
        setActiveMenuItem(activeMenuId);

        // Handle window resize events
        window.addEventListener('resize', handleWindowResize);

        // Initial call to handle the current window size
        handleWindowResize();
    });
}

// Handle window resize
function handleWindowResize() {
    const sidebar = document.querySelector('.sidebar');
    const content = document.querySelector('.content');

    // For desktop view, ensure content has proper margin
    if (window.innerWidth > 768) {
        content.style.marginLeft = sidebar.classList.contains('open') ? 'var(--sidebar-width)' : '0';
        content.style.width = sidebar.classList.contains('open') ? 'calc(100% - var(--sidebar-width))' : '100%';
    } else {
        // For mobile view, rely on the CSS classes
        if (sidebar.classList.contains('open')) {
            content.classList.add('sidebar-open');
        } else {
            content.classList.remove('sidebar-open');
        }
    }
}

// Logout function
function logout() {
    // In a real application, this would clear session/token
    // For this prototype, we'll just redirect to login page
    localStorage.removeItem('user');
    sessionStorage.removeItem('user');

    // Show notification before redirecting
    showNotification('已成功退出登录', 'success');

    // Delay redirect to show notification
    setTimeout(() => {
        window.location.href = 'index.html';
    }, 1000);

    // Prevent default link behavior
    return false;
}
