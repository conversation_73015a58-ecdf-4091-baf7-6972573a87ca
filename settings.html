<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>精准曝气控制系统 (GPAC) - 系统配置</title>
    <link rel="stylesheet" href="css/all.min.css">
    <link rel="stylesheet" href="css/style.css">
    <style>
        .settings-container {
            padding: 20px;
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 20px;
        }
        .settings-card {
            background: white;
            border-radius: 8px;
            padding: 20px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        .settings-title {
            display: flex;
            align-items: center;
            gap: 10px;
            margin-bottom: 20px;
        }
        .settings-title i {
            font-size: 24px;
            color: var(--primary-color);
        }
        .form-divider {
            margin: 20px 0;
            border-top: 1px solid #eee;
        }
        .settings-section {
            margin-bottom: 20px;
        }
        .settings-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 15px;
            margin-top: 10px;
        }
    </style>
</head>
<body>
<script>document.addEventListener("DOMContentLoaded", function() { loadCommonElements(); });</script>
    <div class="container">
        <!-- 顶部栏 -->
        <div class="topbar">
            <div class="topbar-left">
                <div class="toggle-sidebar" onclick="toggleSidebar()">
                    <i class="fas fa-bars"></i>
                </div>
                <img src="logo.png" alt="公司Logo" class="topbar-logo">
                <h2 class="topbar-title">精准曝气控制系统</h2>
            </div>
            <div class="topbar-right">
                <div class="user-dropdown">
                    <div class="user-info">
                        <div class="user-avatar">管</div>
                        <span>管理员</span>
                        <i class="fas fa-chevron-down" style="margin-left: 5px; font-size: 12px;"></i>
                    </div>
                    <div class="dropdown-content">
                        <a href="#"><i class="fas fa-user-circle"></i> 个人信息</a>
                        <a href="#"><i class="fas fa-cog"></i> 账号设置</a>
                        <a href="index.html" onclick="logout()"><i class="fas fa-sign-out-alt"></i> 退出登录</a>
                    </div>
                </div>
            </div>
        </div>

        <!-- 主体内容区域 -->
        <div class="main-container">
            <!-- 侧边栏 -->
            <div class="sidebar">
                <div class="sidebar-menu">
                    <div class="menu-item" id="menu-dashboard" onclick="window.location.href='dashboard.html'">
                        <i class="fas fa-tachometer-alt"></i>
                        <span>实时监控</span>
                    </div>
                    <div class="menu-item" id="menu-model" onclick="window.location.href='model-control.html'">
                        <i class="fas fa-brain"></i>
                        <span>模型控制</span>
                    </div>
                    <div class="menu-item" id="menu-scheduling" onclick="window.location.href='scheduling.html'">
                        <i class="fas fa-tasks"></i>
                        <span>调度管理</span>
                    </div>
                    <div class="menu-item" id="menu-reports" onclick="window.location.href='reports.html'">
                        <i class="fas fa-chart-bar"></i>
                        <span>数据报表</span>
                    </div>
                    <div class="menu-item" id="menu-alarms" onclick="window.location.href='alarms.html'">
                        <i class="fas fa-bell"></i>
                        <span>告警管理</span>
                    </div>
                    <div class="menu-item active" id="menu-settings" onclick="window.location.href='settings.html'">
                        <i class="fas fa-cog"></i>
                        <span>系统配置</span>
                    </div>
                </div>
            </div>

            <!-- 内容区域 -->
            <div class="content">

            <div class="settings-container">
                <!-- 基本设置 -->
                <div class="settings-card">
                    <div class="settings-title">
                        <i class="fas fa-cog"></i>
                        <h3>基本设置</h3>
                    </div>
                    <div class="settings-section">
                        <label class="form-label">系统名称</label>
                        <input type="text" class="form-control" value="精准曝气控制系统">
                    </div>
                    <div class="settings-section">
                        <label class="form-label">数据保存周期（天）</label>
                        <input type="number" class="form-control" value="90">
                    </div>
                    <div class="settings-section">
                        <label class="form-label">自动备份</label>
                        <div class="form-check">
                            <input type="checkbox" class="form-check-input" checked>
                            <label class="form-check-label">启用自动备份</label>
                        </div>
                    </div>
                </div>

                <!-- 控制参数设置 -->
                <div class="settings-card">
                    <div class="settings-title">
                        <i class="fas fa-sliders-h"></i>
                        <h3>控制参数设置</h3>
                    </div>
                    <div class="settings-section">
                        <label class="form-label">DO控制范围</label>
                        <div class="settings-grid">
                            <input type="number" class="form-control" placeholder="最小值" value="1.5">
                            <input type="number" class="form-control" placeholder="最大值" value="3.5">
                        </div>
                    </div>
                    <div class="settings-section">
                        <label class="form-label">控制周期（分钟）</label>
                        <input type="number" class="form-control" value="5">
                    </div>
                    <div class="form-divider"></div>
                    <div class="settings-section">
                        <label class="form-label">模型更新频率</label>
                        <select class="form-control">
                            <option>每小时</option>
                            <option selected>每天</option>
                            <option>每周</option>
                        </select>
                    </div>
                </div>

                <!-- 通知设置 -->
                <div class="settings-card">
                    <div class="settings-title">
                        <i class="fas fa-bell"></i>
                        <h3>通知设置</h3>
                    </div>
                    <div class="settings-section">
                        <label class="form-label">告警通知方式</label>
                        <div class="form-check">
                            <input type="checkbox" class="form-check-input" checked>
                            <label class="form-check-label">系统提醒</label>
                        </div>
                        <div class="form-check">
                            <input type="checkbox" class="form-check-input" checked>
                            <label class="form-check-label">短信通知</label>
                        </div>
                        <div class="form-check">
                            <input type="checkbox" class="form-check-input" checked>
                            <label class="form-check-label">微信推送</label>
                        </div>
                    </div>
                    <div class="settings-section">
                        <label class="form-label">通知接收人</label>
                        <select class="form-control" multiple>
                            <option selected>系统管理员</option>
                            <option selected>运维人员</option>
                            <option>工艺工程师</option>
                        </select>
                    </div>
                </div>

                <!-- 系统维护 -->
                <div class="settings-card">
                    <div class="settings-title">
                        <i class="fas fa-tools"></i>
                        <h3>系统维护</h3>
                    </div>
                    <div class="settings-section">
                        <button class="btn btn-primary">
                            <i class="fas fa-database"></i>
                            备份数据
                        </button>
                        <button class="btn btn-secondary">
                            <i class="fas fa-history"></i>
                            恢复配置
                        </button>
                    </div>
                    <div class="form-divider"></div>
                    <div class="settings-section">
                        <label class="form-label">系统日志级别</label>
                        <select class="form-control">
                            <option>Debug</option>
                            <option selected>Info</option>
                            <option>Warning</option>
                            <option>Error</option>
                        </select>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="js/common.js"></script>
</body>
</html>
