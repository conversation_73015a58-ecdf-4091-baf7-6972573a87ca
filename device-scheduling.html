<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>精准曝气控制系统 (GPAC) - 曝气设备调度</title>
    <link rel="stylesheet" href="css/all.min.css">
    <link rel="stylesheet" href="css/style.css">
    <style>
        /* 确保内容不被侧边栏遮挡 */
        .content {
            margin-left: var(--sidebar-width) !important;
            width: calc(100% - var(--sidebar-width)) !important;
            padding: 20px !important;
            box-sizing: border-box !important;
        }

        /* 子菜单样式 */
        .submenu {
            display: flex;
            flex-wrap: wrap;
            gap: 10px;
            margin-bottom: 20px;
            background-color: #fff;
            padding: 15px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);
        }

        .submenu-item {
            padding: 8px 16px;
            border-radius: 6px;
            cursor: pointer;
            transition: all 0.3s;
            display: flex;
            align-items: center;
            gap: 8px;
            font-size: 14px;
            color: #666;
        }

        .submenu-item:hover {
            background-color: #f5f7fa;
            color: var(--primary-color);
        }

        .submenu-item.active {
            background-color: var(--primary-color);
            color: white;
        }

        /* 标签页样式 */
        .tab-container {
            background-color: #fff;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);
            margin-bottom: 20px;
            overflow: hidden;
        }

        .tab-header {
            display: flex;
            border-bottom: 1px solid #f0f0f0;
            background-color: #fafafa;
        }

        .tab-item {
            padding: 15px 20px;
            cursor: pointer;
            font-weight: 500;
            color: #666;
            transition: all 0.3s;
            position: relative;
        }

        .tab-item:hover {
            color: var(--primary-color);
        }

        .tab-item.active {
            color: var(--primary-color);
        }

        .tab-item.active::after {
            content: '';
            position: absolute;
            bottom: 0;
            left: 0;
            width: 100%;
            height: 2px;
            background-color: var(--primary-color);
        }

        .tab-content {
            padding: 20px;
            display: none;
        }

        .tab-content.active {
            display: block;
        }

        /* 表单样式 */
        .form-section {
            margin-bottom: 25px;
            padding-bottom: 20px;
            border-bottom: 1px solid #f0f0f0;
        }

        .form-section:last-child {
            border-bottom: none;
            margin-bottom: 0;
            padding-bottom: 0;
        }

        .section-title {
            font-size: 16px;
            font-weight: 600;
            margin-bottom: 15px;
            color: #333;
            display: flex;
            align-items: center;
            gap: 8px;
        }

        .form-row {
            display: flex;
            flex-wrap: wrap;
            gap: 20px;
            margin-bottom: 15px;
        }

        .form-group {
            flex: 1;
            min-width: 250px;
        }

        .form-label {
            display: block;
            margin-bottom: 8px;
            font-size: 14px;
            color: #666;
        }

        .form-control {
            width: 100%;
            padding: 10px 12px;
            border: 1px solid #d9d9d9;
            border-radius: 4px;
            font-size: 14px;
            transition: all 0.3s;
        }

        .form-control:hover {
            border-color: #40a9ff;
        }

        .form-control:focus {
            border-color: #1890ff;
            box-shadow: 0 0 0 2px rgba(24, 144, 255, 0.2);
            outline: none;
        }

        .form-text {
            font-size: 12px;
            color: #999;
            margin-top: 5px;
        }

        /* 时间选择器样式 */
        .time-picker {
            display: flex;
            align-items: center;
            gap: 10px;
        }

        .time-picker .form-control {
            width: auto;
        }

        /* 开关样式 */
        .switch {
            position: relative;
            display: inline-block;
            width: 44px;
            height: 22px;
        }

        .switch input {
            opacity: 0;
            width: 0;
            height: 0;
        }

        .slider {
            position: absolute;
            cursor: pointer;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background-color: #ccc;
            transition: .4s;
            border-radius: 22px;
        }

        .slider:before {
            position: absolute;
            content: "";
            height: 18px;
            width: 18px;
            left: 3px;
            bottom: 2px;
            background-color: white;
            transition: .4s;
            border-radius: 50%;
        }

        input:checked + .slider {
            background-color: var(--primary-color);
        }

        input:focus + .slider {
            box-shadow: 0 0 1px var(--primary-color);
        }

        input:checked + .slider:before {
            transform: translateX(20px);
        }

        /* 表格样式 */
        .table-container {
            overflow-x: auto;
            margin-bottom: 20px;
        }

        .data-table {
            width: 100%;
            border-collapse: collapse;
        }

        .data-table th {
            background-color: #fafafa;
            padding: 12px 15px;
            text-align: left;
            font-weight: 500;
            color: #333;
            border-bottom: 1px solid #f0f0f0;
        }

        .data-table td {
            padding: 12px 15px;
            border-bottom: 1px solid #f0f0f0;
        }

        .data-table tr:hover {
            background-color: #f5f7fa;
        }

        /* 按钮样式 */
        .btn-group {
            display: flex;
            gap: 10px;
            margin-top: 20px;
            justify-content: flex-end;
        }

        .btn {
            padding: 8px 16px;
            border-radius: 4px;
            font-size: 14px;
            font-weight: 500;
            cursor: pointer;
            transition: all 0.3s;
            border: none;
        }

        .btn-primary {
            background-color: var(--primary-color);
            color: white;
        }

        .btn-primary:hover {
            background-color: #40a9ff;
            box-shadow: 0 2px 8px rgba(24, 144, 255, 0.3);
        }

        .btn-secondary {
            background-color: #f0f0f0;
            color: #666;
        }

        .btn-secondary:hover {
            background-color: #e0e0e0;
        }

        .btn-danger {
            background-color: #ff4d4f;
            color: white;
        }

        .btn-danger:hover {
            background-color: #ff7875;
            box-shadow: 0 2px 8px rgba(255, 77, 79, 0.3);
        }

        /* 设备卡片样式 */
        .device-cards {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(280px, 1fr));
            gap: 20px;
            margin-bottom: 20px;
        }

        .device-card {
            background-color: #fff;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);
            padding: 15px;
            transition: all 0.3s;
            border: 1px solid #f0f0f0;
        }

        .device-card:hover {
            box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
            transform: translateY(-2px);
        }

        .device-card.active {
            border-color: var(--primary-color);
        }

        .device-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 15px;
            padding-bottom: 10px;
            border-bottom: 1px solid #f0f0f0;
        }

        .device-name {
            font-weight: 600;
            color: #333;
            display: flex;
            align-items: center;
            gap: 8px;
        }

        .device-status {
            display: flex;
            align-items: center;
            gap: 5px;
            font-size: 12px;
            padding: 3px 8px;
            border-radius: 10px;
        }

        .status-running {
            background-color: #f6ffed;
            color: #52c41a;
            border: 1px solid #b7eb8f;
        }

        .status-stopped {
            background-color: #fff2e8;
            color: #fa8c16;
            border: 1px solid #ffd591;
        }

        .status-fault {
            background-color: #fff1f0;
            color: #f5222d;
            border: 1px solid #ffa39e;
        }

        .device-info {
            margin-bottom: 15px;
        }

        .info-item {
            display: flex;
            justify-content: space-between;
            margin-bottom: 8px;
            font-size: 14px;
        }

        .info-label {
            color: #666;
        }

        .info-value {
            font-weight: 500;
            color: #333;
        }

        .device-actions {
            display: flex;
            justify-content: flex-end;
            gap: 10px;
        }

        /* 响应式调整 */
        @media (max-width: 768px) {
            .form-row {
                flex-direction: column;
                gap: 15px;
            }

            .form-group {
                min-width: 100%;
            }

            .submenu {
                overflow-x: auto;
                padding: 10px;
                flex-wrap: nowrap;
            }

            .submenu-item {
                white-space: nowrap;
            }

            .device-cards {
                grid-template-columns: 1fr;
            }
        }
    </style>
</head>
<body>
<script>document.addEventListener("DOMContentLoaded", function() { loadCommonElements(); });</script>
    <div class="container">
        <!-- 顶部栏 -->
        <div class="topbar">
            <div class="topbar-left">
                <div class="toggle-sidebar" onclick="toggleSidebar()">
                    <i class="fas fa-bars"></i>
                </div>
                <img src="logo.png" alt="公司Logo" class="topbar-logo" style="height: 60px; margin-right: 15px; transition: transform 0.3s;" onmouseover="this.style.transform='scale(1.05)'" onmouseout="this.style.transform='scale(1)'">
                <h2 class="topbar-title">精准曝气控制系统</h2>
            </div>
            <div class="topbar-right">
                <div class="user-dropdown">
                    <div class="user-info">
                        <div class="user-avatar">管</div>
                        <span>管理员</span>
                        <i class="fas fa-chevron-down" style="margin-left: 5px; font-size: 12px;"></i>
                    </div>
                    <div class="dropdown-content">
                        <a href="#"><i class="fas fa-user-circle"></i> 个人信息</a>
                        <a href="#"><i class="fas fa-cog"></i> 账号设置</a>
                        <a href="index.html" onclick="logout()"><i class="fas fa-sign-out-alt"></i> 退出登录</a>
                    </div>
                </div>
            </div>
        </div>

        <!-- 主体内容区域 -->
        <div class="main-container">
            <!-- 侧边栏 -->
            <div class="sidebar">
                <div class="sidebar-menu">
                    <div class="menu-item" id="menu-dashboard" onclick="window.location.href='dashboard.html'">
                        <i class="fas fa-tachometer-alt"></i>
                        <span>实时监控</span>
                    </div>
                    <div class="menu-item" id="menu-model" onclick="window.location.href='model-control.html'">
                        <i class="fas fa-brain"></i>
                        <span>模型控制</span>
                    </div>
                    <div class="menu-item active" id="menu-scheduling" onclick="window.location.href='scheduling.html'">
                        <i class="fas fa-tasks"></i>
                        <span>调度管理</span>
                    </div>
                    <div class="menu-item" id="menu-reports" onclick="window.location.href='reports.html'">
                        <i class="fas fa-chart-bar"></i>
                        <span>数据报表</span>
                    </div>
                    <div class="menu-item" id="menu-alarms" onclick="window.location.href='alarms.html'">
                        <i class="fas fa-bell"></i>
                        <span>告警管理</span>
                    </div>
                    <div class="menu-item" id="menu-settings" onclick="window.location.href='settings.html'">
                        <i class="fas fa-cog"></i>
                        <span>系统配置</span>
                    </div>
                </div>
            </div>

            <!-- 内容区域 -->
            <div class="content">
                <!-- 子菜单导航 -->
                <div class="submenu">
                    <div class="submenu-item" onclick="window.location.href='scheduling.html'">
                        <i class="fas fa-home"></i> 调度总览
                    </div>
                    <div class="submenu-item active" onclick="window.location.href='device-scheduling.html'">
                        <i class="fas fa-fan"></i> 曝气设备调度
                    </div>
                    <div class="submenu-item" onclick="window.location.href='do-parameter-scheduling.html'">
                        <i class="fas fa-sliders-h"></i> DO控制参数调度
                    </div>
                    <div class="submenu-item" onclick="window.location.href='energy-optimization.html'">
                        <i class="fas fa-bolt"></i> 能源优化调度
                    </div>
                    <div class="submenu-item" onclick="window.location.href='plan-management.html'">
                        <i class="fas fa-calendar-alt"></i> 调度计划管理
                    </div>
                    <div class="submenu-item" onclick="window.location.href='execution-monitoring.html'">
                        <i class="fas fa-desktop"></i> 执行监控
                    </div>
                    <div class="submenu-item" onclick="window.location.href='statistics-reports.html'">
                        <i class="fas fa-chart-pie"></i> 统计报表
                    </div>
                </div>

                <!-- 页面标题 -->
                <div class="page-header">
                    <h1 class="page-title">
                        <i class="fas fa-fan" style="color: var(--primary-color);"></i>
                        曝气设备调度
                    </h1>
                    <div class="page-actions">
                        <button class="btn btn-primary" onclick="saveAllSettings()">
                            <i class="fas fa-save"></i> 保存所有设置
                        </button>
                    </div>
                </div>

                <!-- 标签页容器 -->
                <div class="tab-container">
                    <div class="tab-header">
                        <div class="tab-item active" data-tab="operation-plan">
                            <i class="fas fa-calendar-alt"></i> 鼓风机运行计划
                        </div>
                        <div class="tab-item" data-tab="device-rotation">
                            <i class="fas fa-sync-alt"></i> 设备轮换
                        </div>
                        <div class="tab-item" data-tab="backup-switching">
                            <i class="fas fa-exchange-alt"></i> 备用切换
                        </div>
                    </div>

                    <!-- 鼓风机运行计划 -->
                    <div class="tab-content active" id="operation-plan">
                        <!-- 日常运行计划 -->
                        <div class="form-section">
                            <h3 class="section-title">
                                <i class="fas fa-calendar-day" style="color: #1890ff;"></i>
                                日常运行计划（工作日）
                            </h3>
                            <div class="form-row">
                                <div class="form-group">
                                    <label class="form-label">计划名称</label>
                                    <input type="text" class="form-control" value="工作日标准运行计划">
                                </div>
                                <div class="form-group">
                                    <label class="form-label">适用日期</label>
                                    <select class="form-control">
                                        <option>周一至周五</option>
                                        <option>周一、周三、周五</option>
                                        <option>周二、周四</option>
                                        <option>自定义</option>
                                    </select>
                                </div>
                                <div class="form-group">
                                    <label class="form-label">计划状态</label>
                                    <div style="display: flex; align-items: center; gap: 10px;">
                                        <label class="switch">
                                            <input type="checkbox" checked>
                                            <span class="slider"></span>
                                        </label>
                                        <span>启用</span>
                                    </div>
                                </div>
                            </div>

                            <div class="form-row">
                                <div class="form-group">
                                    <label class="form-label">运行时段 1</label>
                                    <div class="time-picker">
                                        <input type="time" class="form-control" value="00:00">
                                        <span>至</span>
                                        <input type="time" class="form-control" value="08:00">
                                        <select class="form-control">
                                            <option>低负荷运行</option>
                                            <option>中负荷运行</option>
                                            <option>高负荷运行</option>
                                        </select>
                                    </div>
                                </div>
                            </div>

                            <div class="form-row">
                                <div class="form-group">
                                    <label class="form-label">运行时段 2</label>
                                    <div class="time-picker">
                                        <input type="time" class="form-control" value="08:00">
                                        <span>至</span>
                                        <input type="time" class="form-control" value="18:00">
                                        <select class="form-control">
                                            <option>低负荷运行</option>
                                            <option selected>中负荷运行</option>
                                            <option>高负荷运行</option>
                                        </select>
                                    </div>
                                </div>
                            </div>

                            <div class="form-row">
                                <div class="form-group">
                                    <label class="form-label">运行时段 3</label>
                                    <div class="time-picker">
                                        <input type="time" class="form-control" value="18:00">
                                        <span>至</span>
                                        <input type="time" class="form-control" value="24:00">
                                        <select class="form-control">
                                            <option>低负荷运行</option>
                                            <option>中负荷运行</option>
                                            <option selected>高负荷运行</option>
                                        </select>
                                    </div>
                                </div>
                            </div>

                            <div class="form-row">
                                <div class="form-group">
                                    <button class="btn btn-secondary" onclick="addTimeSlot('workday')">
                                        <i class="fas fa-plus"></i> 添加时段
                                    </button>
                                </div>
                            </div>
                        </div>

                        <!-- 周末运行计划 -->
                        <div class="form-section">
                            <h3 class="section-title">
                                <i class="fas fa-calendar-week" style="color: #52c41a;"></i>
                                周末运行计划
                            </h3>
                            <div class="form-row">
                                <div class="form-group">
                                    <label class="form-label">计划名称</label>
                                    <input type="text" class="form-control" value="周末节能运行计划">
                                </div>
                                <div class="form-group">
                                    <label class="form-label">适用日期</label>
                                    <select class="form-control">
                                        <option>周六、周日</option>
                                        <option>周六</option>
                                        <option>周日</option>
                                        <option>自定义</option>
                                    </select>
                                </div>
                                <div class="form-group">
                                    <label class="form-label">计划状态</label>
                                    <div style="display: flex; align-items: center; gap: 10px;">
                                        <label class="switch">
                                            <input type="checkbox" checked>
                                            <span class="slider"></span>
                                        </label>
                                        <span>启用</span>
                                    </div>
                                </div>
                            </div>

                            <div class="form-row">
                                <div class="form-group">
                                    <label class="form-label">运行时段 1</label>
                                    <div class="time-picker">
                                        <input type="time" class="form-control" value="00:00">
                                        <span>至</span>
                                        <input type="time" class="form-control" value="10:00">
                                        <select class="form-control">
                                            <option selected>低负荷运行</option>
                                            <option>中负荷运行</option>
                                            <option>高负荷运行</option>
                                        </select>
                                    </div>
                                </div>
                            </div>

                            <div class="form-row">
                                <div class="form-group">
                                    <label class="form-label">运行时段 2</label>
                                    <div class="time-picker">
                                        <input type="time" class="form-control" value="10:00">
                                        <span>至</span>
                                        <input type="time" class="form-control" value="24:00">
                                        <select class="form-control">
                                            <option>低负荷运行</option>
                                            <option selected>中负荷运行</option>
                                            <option>高负荷运行</option>
                                        </select>
                                    </div>
                                </div>
                            </div>

                            <div class="form-row">
                                <div class="form-group">
                                    <button class="btn btn-secondary" onclick="addTimeSlot('weekend')">
                                        <i class="fas fa-plus"></i> 添加时段
                                    </button>
                                </div>
                            </div>
                        </div>

                        <!-- 假日运行计划 -->
                        <div class="form-section">
                            <h3 class="section-title">
                                <i class="fas fa-calendar-plus" style="color: #fa8c16;"></i>
                                假日运行计划
                            </h3>
                            <div class="form-row">
                                <div class="form-group">
                                    <label class="form-label">计划名称</label>
                                    <input type="text" class="form-control" value="节假日特殊运行计划">
                                </div>
                                <div class="form-group">
                                    <label class="form-label">适用日期</label>
                                    <div style="display: flex; gap: 10px; align-items: center;">
                                        <button class="btn btn-secondary" onclick="showDatePicker()">
                                            <i class="fas fa-calendar-alt"></i> 选择日期
                                        </button>
                                        <span>已选择 5 个日期</span>
                                    </div>
                                </div>
                                <div class="form-group">
                                    <label class="form-label">计划状态</label>
                                    <div style="display: flex; align-items: center; gap: 10px;">
                                        <label class="switch">
                                            <input type="checkbox" checked>
                                            <span class="slider"></span>
                                        </label>
                                        <span>启用</span>
                                    </div>
                                </div>
                            </div>

                            <div class="form-row">
                                <div class="form-group">
                                    <label class="form-label">运行时段 1</label>
                                    <div class="time-picker">
                                        <input type="time" class="form-control" value="00:00">
                                        <span>至</span>
                                        <input type="time" class="form-control" value="24:00">
                                        <select class="form-control">
                                            <option>低负荷运行</option>
                                            <option selected>中负荷运行</option>
                                            <option>高负荷运行</option>
                                        </select>
                                    </div>
                                </div>
                            </div>

                            <div class="form-row">
                                <div class="form-group">
                                    <button class="btn btn-secondary" onclick="addTimeSlot('holiday')">
                                        <i class="fas fa-plus"></i> 添加时段
                                    </button>
                                </div>
                            </div>
                        </div>

                        <!-- 计划优先级 -->
                        <div class="form-section">
                            <h3 class="section-title">
                                <i class="fas fa-sort-amount-up" style="color: #722ed1;"></i>
                                计划优先级
                            </h3>
                            <p class="form-text">拖动调整计划的优先级顺序，优先级高的计划将覆盖优先级低的计划。</p>

                            <div class="table-container">
                                <table class="data-table">
                                    <thead>
                                        <tr>
                                            <th width="60">优先级</th>
                                            <th>计划名称</th>
                                            <th>适用日期</th>
                                            <th>状态</th>
                                            <th width="100">操作</th>
                                        </tr>
                                    </thead>
                                    <tbody id="priority-list">
                                        <tr>
                                            <td>1</td>
                                            <td>节假日特殊运行计划</td>
                                            <td>特定日期</td>
                                            <td><span class="badge badge-success">启用</span></td>
                                            <td>
                                                <button class="btn btn-sm btn-secondary">
                                                    <i class="fas fa-arrows-alt"></i>
                                                </button>
                                            </td>
                                        </tr>
                                        <tr>
                                            <td>2</td>
                                            <td>周末节能运行计划</td>
                                            <td>周六、周日</td>
                                            <td><span class="badge badge-success">启用</span></td>
                                            <td>
                                                <button class="btn btn-sm btn-secondary">
                                                    <i class="fas fa-arrows-alt"></i>
                                                </button>
                                            </td>
                                        </tr>
                                        <tr>
                                            <td>3</td>
                                            <td>工作日标准运行计划</td>
                                            <td>周一至周五</td>
                                            <td><span class="badge badge-success">启用</span></td>
                                            <td>
                                                <button class="btn btn-sm btn-secondary">
                                                    <i class="fas fa-arrows-alt"></i>
                                                </button>
                                            </td>
                                        </tr>
                                    </tbody>
                                </table>
                            </div>
                        </div>

                        <div class="btn-group">
                            <button class="btn btn-secondary">取消</button>
                            <button class="btn btn-primary" onclick="saveOperationPlan()">保存设置</button>
                        </div>
                    </div>

                    <!-- 设备轮换标签页 -->
                    <div class="tab-content" id="device-rotation">
                        <!-- 轮换周期设置 -->
                        <div class="form-section">
                            <h3 class="section-title">
                                <i class="fas fa-clock" style="color: #1890ff;"></i>
                                轮换周期设置
                            </h3>
                            <div class="form-row">
                                <div class="form-group">
                                    <label class="form-label">轮换周期</label>
                                    <select class="form-control" id="rotation-cycle">
                                        <option value="daily">每日轮换</option>
                                        <option value="weekly" selected>每周轮换</option>
                                        <option value="monthly">每月轮换</option>
                                        <option value="custom">自定义周期</option>
                                    </select>
                                </div>
                                <div class="form-group" id="custom-cycle-group" style="display: none;">
                                    <label class="form-label">自定义周期（小时）</label>
                                    <input type="number" class="form-control" value="168" min="1">
                                </div>
                                <div class="form-group">
                                    <label class="form-label">下次轮换时间</label>
                                    <input type="datetime-local" class="form-control" value="2025-05-15T08:00">
                                </div>
                            </div>

                            <div class="form-row">
                                <div class="form-group">
                                    <label class="form-label">轮换启用状态</label>
                                    <div style="display: flex; align-items: center; gap: 10px;">
                                        <label class="switch">
                                            <input type="checkbox" checked>
                                            <span class="slider"></span>
                                        </label>
                                        <span>启用自动轮换</span>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- 轮换方式 -->
                        <div class="form-section">
                            <h3 class="section-title">
                                <i class="fas fa-random" style="color: #52c41a;"></i>
                                轮换方式
                            </h3>
                            <div class="form-row">
                                <div class="form-group">
                                    <label class="form-label">轮换策略</label>
                                    <select class="form-control" id="rotation-strategy">
                                        <option value="sequential">顺序轮换</option>
                                        <option value="balanced" selected>运行时间均衡</option>
                                        <option value="custom">自定义顺序</option>
                                    </select>
                                    <div class="form-text">顺序轮换按设备编号顺序切换；运行时间均衡优先使用累计运行时间最少的设备</div>
                                </div>
                            </div>

                            <div class="form-row" id="custom-order-group" style="display: none;">
                                <div class="form-group">
                                    <label class="form-label">自定义轮换顺序</label>
                                    <div class="table-container">
                                        <table class="data-table">
                                            <thead>
                                                <tr>
                                                    <th width="60">顺序</th>
                                                    <th>设备名称</th>
                                                    <th width="100">操作</th>
                                                </tr>
                                            </thead>
                                            <tbody id="custom-order-list">
                                                <tr>
                                                    <td>1</td>
                                                    <td>鼓风机 #1</td>
                                                    <td>
                                                        <button class="btn btn-sm btn-secondary">
                                                            <i class="fas fa-arrows-alt"></i>
                                                        </button>
                                                    </td>
                                                </tr>
                                                <tr>
                                                    <td>2</td>
                                                    <td>鼓风机 #2</td>
                                                    <td>
                                                        <button class="btn btn-sm btn-secondary">
                                                            <i class="fas fa-arrows-alt"></i>
                                                        </button>
                                                    </td>
                                                </tr>
                                                <tr>
                                                    <td>3</td>
                                                    <td>鼓风机 #3</td>
                                                    <td>
                                                        <button class="btn btn-sm btn-secondary">
                                                            <i class="fas fa-arrows-alt"></i>
                                                        </button>
                                                    </td>
                                                </tr>
                                            </tbody>
                                        </table>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- 手动切换 -->
                        <div class="form-section">
                            <h3 class="section-title">
                                <i class="fas fa-exchange-alt" style="color: #fa8c16;"></i>
                                手动切换
                            </h3>
                            <p class="form-text">当前运行设备：鼓风机 #2</p>

                            <div class="device-cards">
                                <div class="device-card">
                                    <div class="device-header">
                                        <div class="device-name">
                                            <i class="fas fa-fan"></i> 鼓风机 #1
                                        </div>
                                        <div class="device-status status-stopped">
                                            <i class="fas fa-stop-circle"></i> 已停止
                                        </div>
                                    </div>
                                    <div class="device-info">
                                        <div class="info-item">
                                            <span class="info-label">累计运行时间：</span>
                                            <span class="info-value">1,245 小时</span>
                                        </div>
                                        <div class="info-item">
                                            <span class="info-label">上次运行时间：</span>
                                            <span class="info-value">2025-05-01</span>
                                        </div>
                                        <div class="info-item">
                                            <span class="info-label">设备状态：</span>
                                            <span class="info-value">正常</span>
                                        </div>
                                    </div>
                                    <div class="device-actions">
                                        <button class="btn btn-primary" onclick="switchToDevice(1)">切换到此设备</button>
                                    </div>
                                </div>

                                <div class="device-card active">
                                    <div class="device-header">
                                        <div class="device-name">
                                            <i class="fas fa-fan"></i> 鼓风机 #2
                                        </div>
                                        <div class="device-status status-running">
                                            <i class="fas fa-play-circle"></i> 运行中
                                        </div>
                                    </div>
                                    <div class="device-info">
                                        <div class="info-item">
                                            <span class="info-label">累计运行时间：</span>
                                            <span class="info-value">1,120 小时</span>
                                        </div>
                                        <div class="info-item">
                                            <span class="info-label">当前运行时间：</span>
                                            <span class="info-value">36 小时</span>
                                        </div>
                                        <div class="info-item">
                                            <span class="info-label">设备状态：</span>
                                            <span class="info-value">正常</span>
                                        </div>
                                    </div>
                                    <div class="device-actions">
                                        <button class="btn btn-secondary" disabled>当前运行</button>
                                    </div>
                                </div>

                                <div class="device-card">
                                    <div class="device-header">
                                        <div class="device-name">
                                            <i class="fas fa-fan"></i> 鼓风机 #3
                                        </div>
                                        <div class="device-status status-stopped">
                                            <i class="fas fa-stop-circle"></i> 已停止
                                        </div>
                                    </div>
                                    <div class="device-info">
                                        <div class="info-item">
                                            <span class="info-label">累计运行时间：</span>
                                            <span class="info-value">1,310 小时</span>
                                        </div>
                                        <div class="info-item">
                                            <span class="info-label">上次运行时间：</span>
                                            <span class="info-value">2025-04-25</span>
                                        </div>
                                        <div class="info-item">
                                            <span class="info-label">设备状态：</span>
                                            <span class="info-value">正常</span>
                                        </div>
                                    </div>
                                    <div class="device-actions">
                                        <button class="btn btn-primary" onclick="switchToDevice(3)">切换到此设备</button>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- 轮换记录 -->
                        <div class="form-section">
                            <h3 class="section-title">
                                <i class="fas fa-history" style="color: #722ed1;"></i>
                                轮换记录
                            </h3>

                            <div class="table-container">
                                <table class="data-table">
                                    <thead>
                                        <tr>
                                            <th>轮换时间</th>
                                            <th>轮换类型</th>
                                            <th>从设备</th>
                                            <th>到设备</th>
                                            <th>操作人员</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <tr>
                                            <td>2025-05-08 08:00:00</td>
                                            <td>自动轮换</td>
                                            <td>鼓风机 #1</td>
                                            <td>鼓风机 #2</td>
                                            <td>系统</td>
                                        </tr>
                                        <tr>
                                            <td>2025-05-01 08:00:00</td>
                                            <td>自动轮换</td>
                                            <td>鼓风机 #3</td>
                                            <td>鼓风机 #1</td>
                                            <td>系统</td>
                                        </tr>
                                        <tr>
                                            <td>2025-04-28 14:30:15</td>
                                            <td>手动切换</td>
                                            <td>鼓风机 #2</td>
                                            <td>鼓风机 #3</td>
                                            <td>管理员</td>
                                        </tr>
                                        <tr>
                                            <td>2025-04-24 08:00:00</td>
                                            <td>自动轮换</td>
                                            <td>鼓风机 #1</td>
                                            <td>鼓风机 #2</td>
                                            <td>系统</td>
                                        </tr>
                                        <tr>
                                            <td>2025-04-17 08:00:00</td>
                                            <td>自动轮换</td>
                                            <td>鼓风机 #3</td>
                                            <td>鼓风机 #1</td>
                                            <td>系统</td>
                                        </tr>
                                    </tbody>
                                </table>
                            </div>

                            <div style="text-align: center; margin-top: 15px;">
                                <button class="btn btn-secondary">
                                    <i class="fas fa-file-export"></i> 导出记录
                                </button>
                                <button class="btn btn-secondary">
                                    <i class="fas fa-search"></i> 查看更多
                                </button>
                            </div>
                        </div>

                        <div class="btn-group">
                            <button class="btn btn-secondary">取消</button>
                            <button class="btn btn-primary" onclick="saveRotationSettings()">保存设置</button>
                        </div>
                    </div>

                    <!-- 备用切换标签页 -->
                    <div class="tab-content" id="backup-switching">
                        <!-- 故障自动切换 -->
                        <div class="form-section">
                            <h3 class="section-title">
                                <i class="fas fa-exclamation-triangle" style="color: #f5222d;"></i>
                                故障自动切换
                            </h3>
                            <div class="form-row">
                                <div class="form-group">
                                    <label class="form-label">自动切换状态</label>
                                    <div style="display: flex; align-items: center; gap: 10px;">
                                        <label class="switch">
                                            <input type="checkbox" checked>
                                            <span class="slider"></span>
                                        </label>
                                        <span>启用故障自动切换</span>
                                    </div>
                                </div>
                            </div>

                            <div class="form-row">
                                <div class="form-group">
                                    <label class="form-label">故障检测条件</label>
                                    <div style="margin-bottom: 10px;">
                                        <div style="display: flex; align-items: center; gap: 10px; margin-bottom: 10px;">
                                            <input type="checkbox" checked id="fault-condition-1">
                                            <label for="fault-condition-1">设备通信中断超过</label>
                                            <input type="number" class="form-control" style="width: 80px;" value="30">
                                            <span>秒</span>
                                        </div>
                                        <div style="display: flex; align-items: center; gap: 10px; margin-bottom: 10px;">
                                            <input type="checkbox" checked id="fault-condition-2">
                                            <label for="fault-condition-2">设备报警状态持续超过</label>
                                            <input type="number" class="form-control" style="width: 80px;" value="60">
                                            <span>秒</span>
                                        </div>
                                        <div style="display: flex; align-items: center; gap: 10px; margin-bottom: 10px;">
                                            <input type="checkbox" checked id="fault-condition-3">
                                            <label for="fault-condition-3">设备运行参数超出正常范围</label>
                                            <input type="number" class="form-control" style="width: 80px;" value="120">
                                            <span>秒</span>
                                        </div>
                                        <div style="display: flex; align-items: center; gap: 10px;">
                                            <input type="checkbox" id="fault-condition-4">
                                            <label for="fault-condition-4">设备效率低于</label>
                                            <input type="number" class="form-control" style="width: 80px;" value="70">
                                            <span>%</span>
                                            <input type="number" class="form-control" style="width: 80px;" value="300">
                                            <span>秒</span>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <div class="form-row">
                                <div class="form-group">
                                    <label class="form-label">自动切换规则</label>
                                    <select class="form-control">
                                        <option>切换到下一个可用设备</option>
                                        <option selected>切换到运行时间最少的设备</option>
                                        <option>切换到指定的备用设备</option>
                                    </select>
                                </div>
                                <div class="form-group">
                                    <label class="form-label">指定备用设备</label>
                                    <select class="form-control" disabled>
                                        <option>鼓风机 #1</option>
                                        <option>鼓风机 #3</option>
                                    </select>
                                </div>
                            </div>
                        </div>

                        <!-- 切换延时 -->
                        <div class="form-section">
                            <h3 class="section-title">
                                <i class="fas fa-clock" style="color: #1890ff;"></i>
                                切换延时
                            </h3>
                            <div class="form-row">
                                <div class="form-group">
                                    <label class="form-label">确认延时时间</label>
                                    <div style="display: flex; align-items: center; gap: 10px;">
                                        <input type="number" class="form-control" value="30">
                                        <span>秒</span>
                                    </div>
                                    <div class="form-text">检测到故障后，系统将等待指定时间再执行切换，以避免误报导致的频繁切换</div>
                                </div>
                                <div class="form-group">
                                    <label class="form-label">切换冷却时间</label>
                                    <div style="display: flex; align-items: center; gap: 10px;">
                                        <input type="number" class="form-control" value="300">
                                        <span>秒</span>
                                    </div>
                                    <div class="form-text">两次自动切换之间的最小间隔时间，防止频繁切换</div>
                                </div>
                            </div>

                            <div class="form-row">
                                <div class="form-group">
                                    <label class="form-label">最大切换次数</label>
                                    <div style="display: flex; align-items: center; gap: 10px;">
                                        <input type="number" class="form-control" value="3">
                                        <span>次/天</span>
                                    </div>
                                    <div class="form-text">每天允许的最大自动切换次数，超过此次数将发送警报但不再自动切换</div>
                                </div>
                            </div>
                        </div>

                        <!-- 切换通知 -->
                        <div class="form-section">
                            <h3 class="section-title">
                                <i class="fas fa-bell" style="color: #fa8c16;"></i>
                                切换通知
                            </h3>
                            <div class="form-row">
                                <div class="form-group">
                                    <label class="form-label">通知方式</label>
                                    <div style="margin-bottom: 10px;">
                                        <div style="display: flex; align-items: center; gap: 10px; margin-bottom: 10px;">
                                            <input type="checkbox" checked id="notification-1">
                                            <label for="notification-1">系统内部通知</label>
                                        </div>
                                        <div style="display: flex; align-items: center; gap: 10px; margin-bottom: 10px;">
                                            <input type="checkbox" checked id="notification-2">
                                            <label for="notification-2">短信通知</label>
                                        </div>
                                        <div style="display: flex; align-items: center; gap: 10px;">
                                            <input type="checkbox" checked id="notification-3">
                                            <label for="notification-3">邮件通知</label>
                                        </div>
                                    </div>
                                </div>
                                <div class="form-group">
                                    <label class="form-label">通知接收人</label>
                                    <select class="form-control" multiple style="height: 100px;">
                                        <option selected>系统管理员</option>
                                        <option selected>运维人员</option>
                                        <option>工艺工程师</option>
                                        <option>部门主管</option>
                                    </select>
                                    <div class="form-text">按住Ctrl键可多选</div>
                                </div>
                            </div>

                            <div class="form-row">
                                <div class="form-group">
                                    <label class="form-label">通知内容模板</label>
                                    <textarea class="form-control" rows="3">【精准曝气控制系统】设备{device_name}因{fault_reason}发生故障，系统已自动切换至{new_device}。请及时检查故障设备。</textarea>
                                    <div class="form-text">可使用的变量：{device_name}、{fault_reason}、{fault_time}、{new_device}</div>
                                </div>
                            </div>
                        </div>

                        <!-- 切换测试 -->
                        <div class="form-section">
                            <h3 class="section-title">
                                <i class="fas fa-vial" style="color: #722ed1;"></i>
                                切换测试
                            </h3>
                            <p class="form-text">测试备用切换功能，不会影响实际生产运行</p>

                            <div class="form-row">
                                <div class="form-group">
                                    <label class="form-label">测试设备</label>
                                    <select class="form-control">
                                        <option>鼓风机 #1</option>
                                        <option selected>鼓风机 #2</option>
                                        <option>鼓风机 #3</option>
                                    </select>
                                </div>
                                <div class="form-group">
                                    <label class="form-label">模拟故障类型</label>
                                    <select class="form-control">
                                        <option>通信中断</option>
                                        <option selected>设备报警</option>
                                        <option>参数异常</option>
                                        <option>效率低下</option>
                                    </select>
                                </div>
                            </div>

                            <div style="margin-top: 15px;">
                                <button class="btn btn-primary" onclick="startSwitchingTest()">
                                    <i class="fas fa-play"></i> 开始测试
                                </button>
                                <button class="btn btn-secondary" onclick="viewTestHistory()">
                                    <i class="fas fa-history"></i> 查看测试历史
                                </button>
                            </div>
                        </div>

                        <div class="btn-group">
                            <button class="btn btn-secondary">取消</button>
                            <button class="btn btn-primary" onclick="saveBackupSettings()">保存设置</button>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="js/common.js"></script>
    <script>
        // 初始化公共元素
        document.addEventListener('DOMContentLoaded', function() {
            // 标签页切换
            const tabItems = document.querySelectorAll('.tab-item');
            const tabContents = document.querySelectorAll('.tab-content');

            tabItems.forEach(item => {
                item.addEventListener('click', function() {
                    const tabId = this.getAttribute('data-tab');

                    // 移除所有标签和内容的激活状态
                    tabItems.forEach(tab => tab.classList.remove('active'));
                    tabContents.forEach(content => content.classList.remove('active'));

                    // 激活当前标签和内容
                    this.classList.add('active');
                    document.getElementById(tabId).classList.add('active');
                });
            });

            // 轮换周期选择
            const rotationCycle = document.getElementById('rotation-cycle');
            const customCycleGroup = document.getElementById('custom-cycle-group');

            if (rotationCycle && customCycleGroup) {
                rotationCycle.addEventListener('change', function() {
                    if (this.value === 'custom') {
                        customCycleGroup.style.display = 'block';
                    } else {
                        customCycleGroup.style.display = 'none';
                    }
                });
            }

            // 轮换策略选择
            const rotationStrategy = document.getElementById('rotation-strategy');
            const customOrderGroup = document.getElementById('custom-order-group');

            if (rotationStrategy && customOrderGroup) {
                rotationStrategy.addEventListener('change', function() {
                    if (this.value === 'custom') {
                        customOrderGroup.style.display = 'block';
                    } else {
                        customOrderGroup.style.display = 'none';
                    }
                });
            }
        });

        // 添加时间段
        function addTimeSlot(planType) {
            // 在实际应用中，这里会动态添加新的时间段输入行
            showNotification('添加新的时间段', 'info');

            // 模拟添加新时间段
            const formRow = document.createElement('div');
            formRow.className = 'form-row';
            formRow.innerHTML = `
                <div class="form-group">
                    <label class="form-label">运行时段 新</label>
                    <div class="time-picker">
                        <input type="time" class="form-control" value="00:00">
                        <span>至</span>
                        <input type="time" class="form-control" value="00:00">
                        <select class="form-control">
                            <option>低负荷运行</option>
                            <option>中负荷运行</option>
                            <option>高负荷运行</option>
                        </select>
                        <button class="btn btn-sm btn-danger" onclick="removeTimeSlot(this)">
                            <i class="fas fa-times"></i>
                        </button>
                    </div>
                </div>
            `;

            // 找到对应计划类型的添加按钮所在的行
            const addButtonRow = document.querySelector(`button[onclick="addTimeSlot('${planType}')"]`).closest('.form-row');

            // 在添加按钮行之前插入新的时间段行
            addButtonRow.parentNode.insertBefore(formRow, addButtonRow);
        }

        // 移除时间段
        function removeTimeSlot(button) {
            const formRow = button.closest('.form-row');
            formRow.parentNode.removeChild(formRow);
            showNotification('已移除时间段', 'info');
        }

        // 保存运行计划
        function saveOperationPlan() {
            showNotification('正在保存运行计划...', 'info');

            // 模拟保存延迟
            setTimeout(function() {
                showNotification('运行计划已保存', 'success');
            }, 1000);
        }

        // 保存轮换设置
        function saveRotationSettings() {
            showNotification('正在保存轮换设置...', 'info');

            // 模拟保存延迟
            setTimeout(function() {
                showNotification('轮换设置已保存', 'success');
            }, 1000);
        }

        // 保存备用切换设置
        function saveBackupSettings() {
            showNotification('正在保存备用切换设置...', 'info');

            // 模拟保存延迟
            setTimeout(function() {
                showNotification('备用切换设置已保存', 'success');
            }, 1000);
        }

        // 保存所有设置
        function saveAllSettings() {
            showNotification('正在保存所有设置...', 'info');

            // 模拟保存延迟
            setTimeout(function() {
                showNotification('所有设置已保存', 'success');
            }, 1500);
        }

        // 切换到指定设备
        function switchToDevice(deviceId) {
            showNotification(`正在切换到鼓风机 #${deviceId}...`, 'info');

            // 模拟切换延迟
            setTimeout(function() {
                showNotification(`已成功切换到鼓风机 #${deviceId}`, 'success');

                // 更新设备卡片状态
                const deviceCards = document.querySelectorAll('.device-card');
                deviceCards.forEach(card => {
                    card.classList.remove('active');

                    // 更新状态标签
                    const statusDiv = card.querySelector('.device-status');
                    if (statusDiv) {
                        statusDiv.className = 'device-status status-stopped';
                        statusDiv.innerHTML = '<i class="fas fa-stop-circle"></i> 已停止';
                    }

                    // 更新按钮
                    const actionButton = card.querySelector('.device-actions button');
                    if (actionButton) {
                        actionButton.className = 'btn btn-primary';
                        actionButton.disabled = false;
                        actionButton.innerHTML = '切换到此设备';
                        actionButton.onclick = function() { switchToDevice(card.querySelector('.device-name').textContent.trim().split('#')[1]); };
                    }
                });

                // 激活选中的设备卡片
                const selectedCard = document.querySelector(`.device-card:nth-child(${deviceId})`);
                if (selectedCard) {
                    selectedCard.classList.add('active');

                    // 更新状态标签
                    const statusDiv = selectedCard.querySelector('.device-status');
                    if (statusDiv) {
                        statusDiv.className = 'device-status status-running';
                        statusDiv.innerHTML = '<i class="fas fa-play-circle"></i> 运行中';
                    }

                    // 更新按钮
                    const actionButton = selectedCard.querySelector('.device-actions button');
                    if (actionButton) {
                        actionButton.className = 'btn btn-secondary';
                        actionButton.disabled = true;
                        actionButton.innerHTML = '当前运行';
                    }
                }

                // 更新当前运行设备文本
                const currentDeviceText = document.querySelector('.form-section:nth-child(3) .form-text');
                if (currentDeviceText) {
                    currentDeviceText.textContent = `当前运行设备：鼓风机 #${deviceId}`;
                }
            }, 2000);
        }

        // 开始切换测试
        function startSwitchingTest() {
            showNotification('正在开始备用切换测试...', 'info');

            // 模拟测试过程
            setTimeout(function() {
                showNotification('备用切换测试成功完成', 'success');
            }, 3000);
        }

        // 查看测试历史
        function viewTestHistory() {
            showNotification('正在加载测试历史记录...', 'info');

            // 模拟加载延迟
            setTimeout(function() {
                // 在实际应用中，这里会显示测试历史记录
                alert('测试历史记录：\n1. 2025-05-08 10:15 - 通信中断测试 - 成功\n2. 2025-05-05 14:30 - 设备报警测试 - 成功\n3. 2025-05-01 09:45 - 参数异常测试 - 失败');
                showNotification('测试历史记录已加载', 'success');
            }, 1000);
        }

        // 显示日期选择器
        function showDatePicker() {
            showNotification('正在打开日期选择器...', 'info');

            // 在实际应用中，这里会显示日期选择器
            alert('请选择节假日日期：\n- 2025-01-01 元旦\n- 2025-02-09 春节\n- 2025-05-01 劳动节\n- 2025-10-01 国庆节\n- 2025-10-07 中秋节');

            showNotification('已选择 5 个节假日', 'success');
        }

        // 显示通知
        function showNotification(message, type) {
            // 假设common.js中已经实现了这个函数
            if (window.showNotification) {
                window.showNotification(message, type);
            } else {
                // 简单的通知实现
                const notification = document.createElement('div');
                notification.className = `notification ${type}`;
                notification.innerHTML = `
                    <div class="notification-content">
                        <i class="fas fa-${type === 'success' ? 'check-circle' : type === 'error' ? 'times-circle' : 'info-circle'}"></i>
                        <span>${message}</span>
                    </div>
                `;
                document.body.appendChild(notification);

                // 显示通知
                setTimeout(() => notification.classList.add('show'), 10);

                // 3秒后隐藏通知
                setTimeout(() => {
                    notification.classList.remove('show');
                    setTimeout(() => document.body.removeChild(notification), 300);
                }, 3000);
            }
        }
    </script>
</body>
</html>
