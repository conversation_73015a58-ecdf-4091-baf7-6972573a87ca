#!/bin/bash

# 遍历所有HTML文件
for file in *.html; do
    # 跳过index.html，因为它是登录页面，结构不同
    if [ "$file" == "index.html" ]; then
        echo "跳过 $file (登录页面)"
        continue
    fi
    
    echo "检查 $file..."
    
    # 检查是否包含CSS和JS引用
    if ! grep -q "css/style.css" "$file"; then
        echo "  缺少样式表引用，添加中..."
        # 在</head>前添加样式表引用
        sed -i '' 's/<\/head>/<link rel="stylesheet" href="css\/style.css">\n<\/head>/' "$file"
    fi
    
    if ! grep -q "js/common.js" "$file"; then
        echo "  缺少脚本引用，添加中..."
        # 在</body>前添加脚本引用
        sed -i '' 's/<\/body>/<script src="js\/common.js"><\/script>\n<\/body>/' "$file"
    fi
    
    # 检查是否调用了loadCommonElements函数
    if ! grep -q "loadCommonElements()" "$file"; then
        echo "  缺少loadCommonElements()调用，添加中..."
        # 在body标签后添加脚本调用
        sed -i '' 's/<body>/<body>\n<script>document.addEventListener("DOMContentLoaded", function() { loadCommonElements(); });<\/script>/' "$file"
    fi
    
    echo "  $file 检查完成"
done

echo "所有页面检查完成"
