<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>精准曝气控制系统 (GPAC) - 统计报表</title>
    <link rel="stylesheet" href="css/all.min.css">
    <link rel="stylesheet" href="css/style.css">
    <style>
        /* 子菜单样式 */
        .submenu {
            display: flex;
            flex-wrap: wrap;
            gap: 10px;
            margin-bottom: 20px;
            background-color: #fff;
            padding: 15px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);
        }

        .submenu-item {
            padding: 8px 16px;
            border-radius: 6px;
            cursor: pointer;
            transition: all 0.3s;
            display: flex;
            align-items: center;
            gap: 8px;
            font-size: 14px;
            color: #666;
        }

        .submenu-item:hover {
            background-color: #f5f7fa;
            color: var(--primary-color);
        }

        .submenu-item.active {
            background-color: var(--primary-color);
            color: white;
        }

        /* 标签页样式 */
        .tab-container {
            background-color: #fff;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);
            margin-bottom: 20px;
            overflow: hidden;
        }

        .tab-header {
            display: flex;
            border-bottom: 1px solid #f0f0f0;
            background-color: #fafafa;
        }

        .tab-item {
            padding: 15px 20px;
            cursor: pointer;
            font-weight: 500;
            color: #666;
            transition: all 0.3s;
            position: relative;
        }

        .tab-item:hover {
            color: var(--primary-color);
        }

        .tab-item.active {
            color: var(--primary-color);
        }

        .tab-item.active::after {
            content: '';
            position: absolute;
            bottom: 0;
            left: 0;
            width: 100%;
            height: 2px;
            background-color: var(--primary-color);
        }

        .tab-content {
            padding: 20px;
            display: none;
        }

        .tab-content.active {
            display: block;
        }

        /* 表单样式 */
        .form-section {
            margin-bottom: 25px;
            padding-bottom: 20px;
            border-bottom: 1px solid #f0f0f0;
        }

        .form-section:last-child {
            border-bottom: none;
            margin-bottom: 0;
            padding-bottom: 0;
        }

        .section-title {
            font-size: 16px;
            font-weight: 600;
            margin-bottom: 15px;
            color: #333;
            display: flex;
            align-items: center;
            gap: 8px;
        }

        .form-row {
            display: flex;
            flex-wrap: wrap;
            gap: 20px;
            margin-bottom: 15px;
        }

        .form-group {
            flex: 1;
            min-width: 250px;
        }

        .form-label {
            display: block;
            margin-bottom: 8px;
            font-size: 14px;
            color: #666;
        }

        .form-control {
            width: 100%;
            padding: 10px 12px;
            border: 1px solid #d9d9d9;
            border-radius: 4px;
            font-size: 14px;
            transition: all 0.3s;
        }

        .form-control:hover {
            border-color: #40a9ff;
        }

        .form-control:focus {
            border-color: #1890ff;
            box-shadow: 0 0 0 2px rgba(24, 144, 255, 0.2);
            outline: none;
        }

        .form-text {
            font-size: 12px;
            color: #999;
            margin-top: 5px;
        }

        /* 表格样式 */
        .table-container {
            overflow-x: auto;
            margin-bottom: 20px;
        }

        .data-table {
            width: 100%;
            border-collapse: collapse;
        }

        .data-table th {
            background-color: #fafafa;
            padding: 12px 15px;
            text-align: left;
            font-weight: 500;
            color: #333;
            border-bottom: 1px solid #f0f0f0;
        }

        .data-table td {
            padding: 12px 15px;
            border-bottom: 1px solid #f0f0f0;
        }

        .data-table tr:hover {
            background-color: #f5f7fa;
        }

        /* 按钮样式 */
        .btn-group {
            display: flex;
            gap: 10px;
            margin-top: 20px;
            justify-content: flex-end;
        }

        .btn {
            padding: 8px 16px;
            border-radius: 4px;
            font-size: 14px;
            font-weight: 500;
            cursor: pointer;
            transition: all 0.3s;
            border: none;
        }

        .btn-primary {
            background-color: var(--primary-color);
            color: white;
        }

        .btn-primary:hover {
            background-color: #40a9ff;
            box-shadow: 0 2px 8px rgba(24, 144, 255, 0.3);
        }

        .btn-secondary {
            background-color: #f0f0f0;
            color: #666;
        }

        .btn-secondary:hover {
            background-color: #e0e0e0;
        }

        /* 图表容器样式 */
        .chart-container {
            height: 300px;
            margin-bottom: 20px;
            background-color: #fff;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);
            padding: 15px;
        }

        /* 卡片样式 */
        .card-container {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 20px;
            margin-bottom: 20px;
        }

        .card {
            background-color: #fff;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);
            padding: 15px;
            transition: all 0.3s;
        }

        .card:hover {
            box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
            transform: translateY(-2px);
        }

        .card-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 15px;
            padding-bottom: 10px;
            border-bottom: 1px solid #f0f0f0;
        }

        .card-title {
            font-weight: 600;
            color: #333;
            display: flex;
            align-items: center;
            gap: 8px;
            margin: 0;
        }

        .card-value {
            font-size: 24px;
            font-weight: 600;
            color: #333;
            margin: 10px 0;
        }

        .card-footer {
            display: flex;
            justify-content: space-between;
            align-items: center;
            font-size: 14px;
            color: #666;
        }

        .card-trend {
            display: flex;
            align-items: center;
            gap: 5px;
        }

        .card-trend.up {
            color: #52c41a;
        }

        .card-trend.down {
            color: #f5222d;
        }

        /* 响应式调整 */
        @media (max-width: 768px) {
            .form-row {
                flex-direction: column;
                gap: 15px;
            }

            .form-group {
                min-width: 100%;
            }

            .submenu {
                overflow-x: auto;
                padding: 10px;
                flex-wrap: nowrap;
            }

            .submenu-item {
                white-space: nowrap;
            }

            .card-container {
                grid-template-columns: 1fr;
            }
        }
    </style>
</head>
<body>
<script>document.addEventListener("DOMContentLoaded", function() { loadCommonElements(); });</script>
    <div class="container">
        <!-- 顶部栏 -->
        <div class="topbar">
            <div class="topbar-left">
                <div class="toggle-sidebar" onclick="toggleSidebar()">
                    <i class="fas fa-bars"></i>
                </div>
                <img src="logo.png" alt="公司Logo" class="topbar-logo" style="height: 60px; margin-right: 15px; transition: transform 0.3s;" onmouseover="this.style.transform='scale(1.05)'" onmouseout="this.style.transform='scale(1)'">
                <h2 class="topbar-title">精准曝气控制系统</h2>
            </div>
            <div class="topbar-right">
                <div class="user-dropdown">
                    <div class="user-info">
                        <div class="user-avatar">管</div>
                        <span>管理员</span>
                        <i class="fas fa-chevron-down" style="margin-left: 5px; font-size: 12px;"></i>
                    </div>
                    <div class="dropdown-content">
                        <a href="#"><i class="fas fa-user-circle"></i> 个人信息</a>
                        <a href="#"><i class="fas fa-cog"></i> 账号设置</a>
                        <a href="index.html" onclick="logout()"><i class="fas fa-sign-out-alt"></i> 退出登录</a>
                    </div>
                </div>
            </div>
        </div>

        <!-- 主体内容区域 -->
        <div class="main-container">
            <!-- 侧边栏 -->
            <div class="sidebar">
                <div class="sidebar-menu">
                    <div class="menu-item" id="menu-dashboard" onclick="window.location.href='dashboard.html'">
                        <i class="fas fa-tachometer-alt"></i>
                        <span>实时监控</span>
                    </div>
                    <div class="menu-item" id="menu-model" onclick="window.location.href='model-control.html'">
                        <i class="fas fa-brain"></i>
                        <span>模型控制</span>
                    </div>
                    <div class="menu-item active" id="menu-scheduling" onclick="window.location.href='scheduling.html'">
                        <i class="fas fa-tasks"></i>
                        <span>调度管理</span>
                    </div>
                    <div class="menu-item" id="menu-reports" onclick="window.location.href='reports.html'">
                        <i class="fas fa-chart-bar"></i>
                        <span>数据报表</span>
                    </div>
                    <div class="menu-item" id="menu-alarms" onclick="window.location.href='alarms.html'">
                        <i class="fas fa-bell"></i>
                        <span>告警管理</span>
                    </div>
                    <div class="menu-item" id="menu-settings" onclick="window.location.href='settings.html'">
                        <i class="fas fa-cog"></i>
                        <span>系统配置</span>
                    </div>
                </div>
            </div>

            <!-- 内容区域 -->
            <div class="content" style="margin-left: var(--sidebar-width); width: calc(100% - var(--sidebar-width)); padding: 20px;">
                <!-- 子菜单导航 -->
                <div class="submenu">
                    <div class="submenu-item" onclick="window.location.href='scheduling.html'">
                        <i class="fas fa-home"></i> 调度总览
                    </div>
                    <div class="submenu-item" onclick="window.location.href='device-scheduling.html'">
                        <i class="fas fa-fan"></i> 曝气设备调度
                    </div>
                    <div class="submenu-item" onclick="window.location.href='do-parameter-scheduling.html'">
                        <i class="fas fa-sliders-h"></i> DO控制参数调度
                    </div>
                    <div class="submenu-item" onclick="window.location.href='energy-optimization.html'">
                        <i class="fas fa-bolt"></i> 能源优化调度
                    </div>
                    <div class="submenu-item" onclick="window.location.href='plan-management.html'">
                        <i class="fas fa-calendar-alt"></i> 调度计划管理
                    </div>
                    <div class="submenu-item" onclick="window.location.href='execution-monitoring.html'">
                        <i class="fas fa-desktop"></i> 执行监控
                    </div>
                    <div class="submenu-item active" onclick="window.location.href='statistics-reports.html'">
                        <i class="fas fa-chart-pie"></i> 统计报表
                    </div>
                </div>

                <!-- 页面标题 -->
                <div class="page-header">
                    <h1 class="page-title">
                        <i class="fas fa-chart-pie" style="color: var(--primary-color);"></i>
                        统计报表
                    </h1>
                    <div class="page-actions">
                        <button class="btn btn-primary" onclick="exportReport()">
                            <i class="fas fa-file-export"></i> 导出报表
                        </button>
                    </div>
                </div>

                <!-- 标签页容器 -->
                <div class="tab-container">
                    <div class="tab-header">
                        <div class="tab-item active" data-tab="operation-stats">
                            <i class="fas fa-cogs"></i> 运行统计
                        </div>
                        <div class="tab-item" data-tab="energy-stats">
                            <i class="fas fa-bolt"></i> 能耗统计
                        </div>
                        <div class="tab-item" data-tab="effect-evaluation">
                            <i class="fas fa-chart-line"></i> 效果评估
                        </div>
                    </div>

                    <!-- 运行统计标签页 -->
                    <div class="tab-content active" id="operation-stats">
                        <!-- 时间范围选择 -->
                        <div class="form-section">
                            <div class="form-row">
                                <div class="form-group">
                                    <label class="form-label">统计时间范围</label>
                                    <div style="display: flex; align-items: center; gap: 10px;">
                                        <input type="date" class="form-control" value="2025-04-15">
                                        <span>至</span>
                                        <input type="date" class="form-control" value="2025-05-15">
                                        <button class="btn btn-primary" style="margin-left: 10px;" onclick="updateOperationStats()">
                                            <i class="fas fa-sync-alt"></i> 更新
                                        </button>
                                    </div>
                                </div>
                                <div class="form-group">
                                    <label class="form-label">快速选择</label>
                                    <div style="display: flex; gap: 10px;">
                                        <button class="btn btn-secondary" onclick="selectTimeRange('today')">今日</button>
                                        <button class="btn btn-secondary" onclick="selectTimeRange('week')">本周</button>
                                        <button class="btn btn-secondary" onclick="selectTimeRange('month')">本月</button>
                                        <button class="btn btn-secondary" onclick="selectTimeRange('quarter')">本季度</button>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- 设备运行时间 -->
                        <div class="form-section">
                            <h3 class="section-title">
                                <i class="fas fa-clock" style="color: #1890ff;"></i>
                                设备运行时间
                            </h3>
                            <div class="card-container">
                                <div class="card">
                                    <div class="card-header">
                                        <h4 class="card-title">
                                            <i class="fas fa-fan" style="color: #1890ff;"></i>
                                            鼓风机1#
                                        </h4>
                                        <div style="font-size: 14px; color: #666;">设备ID: BL-001</div>
                                    </div>
                                    <div class="card-value">168.5 小时</div>
                                    <div class="card-footer">
                                        <div>运行率: 93.6%</div>
                                        <div class="card-trend up">
                                            <i class="fas fa-arrow-up"></i>
                                            <span>2.3%</span>
                                        </div>
                                    </div>
                                </div>

                                <div class="card">
                                    <div class="card-header">
                                        <h4 class="card-title">
                                            <i class="fas fa-fan" style="color: #1890ff;"></i>
                                            鼓风机2#
                                        </h4>
                                        <div style="font-size: 14px; color: #666;">设备ID: BL-002</div>
                                    </div>
                                    <div class="card-value">142.8 小时</div>
                                    <div class="card-footer">
                                        <div>运行率: 79.3%</div>
                                        <div class="card-trend down">
                                            <i class="fas fa-arrow-down"></i>
                                            <span>1.5%</span>
                                        </div>
                                    </div>
                                </div>

                                <div class="card">
                                    <div class="card-header">
                                        <h4 class="card-title">
                                            <i class="fas fa-fan" style="color: #1890ff;"></i>
                                            鼓风机3#
                                        </h4>
                                        <div style="font-size: 14px; color: #666;">设备ID: BL-003</div>
                                    </div>
                                    <div class="card-value">156.2 小时</div>
                                    <div class="card-footer">
                                        <div>运行率: 86.8%</div>
                                        <div class="card-trend up">
                                            <i class="fas fa-arrow-up"></i>
                                            <span>3.1%</span>
                                        </div>
                                    </div>
                                </div>

                                <div class="card">
                                    <div class="card-header">
                                        <h4 class="card-title">
                                            <i class="fas fa-fan" style="color: #1890ff;"></i>
                                            鼓风机4#
                                        </h4>
                                        <div style="font-size: 14px; color: #666;">设备ID: BL-004</div>
                                    </div>
                                    <div class="card-value">72.5 小时</div>
                                    <div class="card-footer">
                                        <div>运行率: 40.3%</div>
                                        <div class="card-trend down">
                                            <i class="fas fa-arrow-down"></i>
                                            <span>5.2%</span>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <div class="chart-container">
                                <div style="text-align: center; padding: 20px;">
                                    <i class="fas fa-chart-bar" style="font-size: 48px; color: #d9d9d9;"></i>
                                    <p>设备运行时间对比图将在此显示</p>
                                </div>
                            </div>
                        </div>

                        <!-- 启停次数 -->
                        <div class="form-section">
                            <h3 class="section-title">
                                <i class="fas fa-power-off" style="color: #52c41a;"></i>
                                启停次数
                            </h3>
                            <div class="table-container">
                                <table class="data-table">
                                    <thead>
                                        <tr>
                                            <th>设备名称</th>
                                            <th>设备ID</th>
                                            <th>启动次数</th>
                                            <th>停止次数</th>
                                            <th>平均运行时长</th>
                                            <th>最长连续运行</th>
                                            <th>最短运行时长</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <tr>
                                            <td>鼓风机1#</td>
                                            <td>BL-001</td>
                                            <td>12</td>
                                            <td>11</td>
                                            <td>14.0 小时</td>
                                            <td>48.5 小时</td>
                                            <td>2.5 小时</td>
                                        </tr>
                                        <tr>
                                            <td>鼓风机2#</td>
                                            <td>BL-002</td>
                                            <td>18</td>
                                            <td>17</td>
                                            <td>7.9 小时</td>
                                            <td>24.2 小时</td>
                                            <td>1.8 小时</td>
                                        </tr>
                                        <tr>
                                            <td>鼓风机3#</td>
                                            <td>BL-003</td>
                                            <td>15</td>
                                            <td>14</td>
                                            <td>10.4 小时</td>
                                            <td>36.8 小时</td>
                                            <td>2.2 小时</td>
                                        </tr>
                                        <tr>
                                            <td>鼓风机4#</td>
                                            <td>BL-004</td>
                                            <td>24</td>
                                            <td>24</td>
                                            <td>3.0 小时</td>
                                            <td>12.5 小时</td>
                                            <td>0.8 小时</td>
                                        </tr>
                                    </tbody>
                                </table>
                            </div>

                            <div class="chart-container">
                                <div style="text-align: center; padding: 20px;">
                                    <i class="fas fa-chart-line" style="font-size: 48px; color: #d9d9d9;"></i>
                                    <p>设备启停次数趋势图将在此显示</p>
                                </div>
                            </div>
                        </div>

                        <!-- 负载分布 -->
                        <div class="form-section">
                            <h3 class="section-title">
                                <i class="fas fa-balance-scale" style="color: #fa8c16;"></i>
                                负载分布
                            </h3>
                            <div class="form-row">
                                <div class="form-group">
                                    <label class="form-label">负载类型</label>
                                    <select class="form-control" onchange="updateLoadDistribution()">
                                        <option value="power" selected>功率负载</option>
                                        <option value="flow">风量负载</option>
                                        <option value="pressure">压力负载</option>
                                    </select>
                                </div>
                                <div class="form-group">
                                    <label class="form-label">统计粒度</label>
                                    <select class="form-control" onchange="updateLoadDistribution()">
                                        <option value="hour">小时</option>
                                        <option value="day" selected>天</option>
                                        <option value="week">周</option>
                                    </select>
                                </div>
                            </div>

                            <div class="chart-container">
                                <div style="text-align: center; padding: 20px;">
                                    <i class="fas fa-chart-pie" style="font-size: 48px; color: #d9d9d9;"></i>
                                    <p>设备负载分布图将在此显示</p>
                                </div>
                            </div>

                            <div class="table-container">
                                <table class="data-table">
                                    <thead>
                                        <tr>
                                            <th>设备名称</th>
                                            <th>平均负载</th>
                                            <th>最大负载</th>
                                            <th>最小负载</th>
                                            <th>低负载时间占比</th>
                                            <th>中负载时间占比</th>
                                            <th>高负载时间占比</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <tr>
                                            <td>鼓风机1#</td>
                                            <td>68.5%</td>
                                            <td>92.3%</td>
                                            <td>45.2%</td>
                                            <td>15.3%</td>
                                            <td>62.8%</td>
                                            <td>21.9%</td>
                                        </tr>
                                        <tr>
                                            <td>鼓风机2#</td>
                                            <td>72.1%</td>
                                            <td>95.8%</td>
                                            <td>48.6%</td>
                                            <td>12.5%</td>
                                            <td>58.2%</td>
                                            <td>29.3%</td>
                                        </tr>
                                        <tr>
                                            <td>鼓风机3#</td>
                                            <td>65.7%</td>
                                            <td>88.4%</td>
                                            <td>42.1%</td>
                                            <td>18.7%</td>
                                            <td>65.3%</td>
                                            <td>16.0%</td>
                                        </tr>
                                        <tr>
                                            <td>鼓风机4#</td>
                                            <td>58.2%</td>
                                            <td>82.5%</td>
                                            <td>38.9%</td>
                                            <td>25.4%</td>
                                            <td>68.7%</td>
                                            <td>5.9%</td>
                                        </tr>
                                    </tbody>
                                </table>
                            </div>
                        </div>

                        <!-- 运行趋势 -->
                        <div class="form-section">
                            <h3 class="section-title">
                                <i class="fas fa-chart-line" style="color: #722ed1;"></i>
                                运行趋势
                            </h3>
                            <div class="form-row">
                                <div class="form-group">
                                    <label class="form-label">趋势参数</label>
                                    <select class="form-control" onchange="updateOperationTrend()">
                                        <option value="do" selected>DO浓度</option>
                                        <option value="power">功率</option>
                                        <option value="flow">风量</option>
                                        <option value="pressure">压力</option>
                                        <option value="temperature">温度</option>
                                    </select>
                                </div>
                                <div class="form-group">
                                    <label class="form-label">显示设备</label>
                                    <div style="display: flex; gap: 10px; flex-wrap: wrap;">
                                        <label style="display: flex; align-items: center; gap: 5px;">
                                            <input type="checkbox" checked> 鼓风机1#
                                        </label>
                                        <label style="display: flex; align-items: center; gap: 5px;">
                                            <input type="checkbox" checked> 鼓风机2#
                                        </label>
                                        <label style="display: flex; align-items: center; gap: 5px;">
                                            <input type="checkbox" checked> 鼓风机3#
                                        </label>
                                        <label style="display: flex; align-items: center; gap: 5px;">
                                            <input type="checkbox"> 鼓风机4#
                                        </label>
                                    </div>
                                </div>
                            </div>

                            <div class="chart-container" style="height: 400px;">
                                <div style="text-align: center; padding: 20px;">
                                    <i class="fas fa-chart-line" style="font-size: 48px; color: #d9d9d9;"></i>
                                    <p>运行参数趋势图将在此显示</p>
                                </div>
                            </div>

                            <div class="form-row">
                                <div class="form-group">
                                    <label class="form-label">统计分析</label>
                                    <div style="background-color: #f9f9f9; border-radius: 8px; padding: 15px;">
                                        <p style="margin: 0 0 10px 0;"><strong>DO浓度分析：</strong></p>
                                        <ul style="margin: 0; padding-left: 20px;">
                                            <li>平均DO浓度: 2.8 mg/L，符合工艺要求</li>
                                            <li>DO波动范围: 1.5-3.5 mg/L，波动较小</li>
                                            <li>DO控制稳定性良好，控制精度达到±0.2 mg/L</li>
                                            <li>夜间DO浓度适当降低，符合节能运行策略</li>
                                            <li>高负荷时段DO浓度提高，保证处理效果</li>
                                        </ul>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- 能耗统计标签页 -->
                    <div class="tab-content" id="energy-stats">
                        <!-- 内容将在下一步添加 -->
                    </div>

                    <!-- 效果评估标签页 -->
                    <div class="tab-content" id="effect-evaluation">
                        <!-- 内容将在下一步添加 -->
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="js/common.js"></script>
    <script>
        // 初始化公共元素
        document.addEventListener('DOMContentLoaded', function() {
            // 加载公共元素
            loadCommonElements();

            // 标签页切换
            const tabItems = document.querySelectorAll('.tab-item');
            const tabContents = document.querySelectorAll('.tab-content');

            tabItems.forEach(item => {
                item.addEventListener('click', function() {
                    const tabId = this.getAttribute('data-tab');

                    // 移除所有标签和内容的激活状态
                    tabItems.forEach(tab => tab.classList.remove('active'));
                    tabContents.forEach(content => content.classList.remove('active'));

                    // 激活当前标签和内容
                    this.classList.add('active');
                    document.getElementById(tabId).classList.add('active');
                });
            });
        });

        // 导出报表
        function exportReport() {
            alert('报表导出功能将在此实现');
        }
    </script>
</body>
</html>
