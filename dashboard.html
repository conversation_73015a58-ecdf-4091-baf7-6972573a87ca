<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>精准曝气控制系统 (GPAC) - 实时监控</title>
    <link rel="stylesheet" href="css/all.min.css">
    <link rel="stylesheet" href="css/style.css">
    <style>
        /* 状态指示器样式 */
        .status-indicator {
            display: inline-block;
            width: 10px;
            height: 10px;
            border-radius: 50%;
            margin-right: 5px;
        }
        .status-normal {
            background-color: #52c41a;
        }
        .status-warning {
            background-color: #faad14;
        }
        .status-danger {
            background-color: #f5222d;
        }

        /* 通知样式 */
        .notification {
            position: fixed;
            top: 20px;
            right: 20px;
            padding: 15px;
            background-color: white;
            border-radius: 4px;
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
            transform: translateX(calc(100% + 20px));
            transition: transform 0.3s ease-in-out;
            z-index: 1001;
        }
         .content {
            margin-left: var(--sidebar-width) !important;
            width: calc(100% - var(--sidebar-width)) !important;
            padding: 20px !important;
            box-sizing: border-box !important;
        }

        .notification.show {
            transform: translateX(0);
        }
        .notification-content {
            display: flex;
            align-items: center;
        }
        .notification-content i {
            margin-right: 10px;
            font-size: 18px;
        }
        .notification.success i {
            color: #52c41a;
        }
        .notification.error i {
            color: #f5222d;
        }
        .notification.info i {
            color: #1890ff;
        }

        /* 内容区域美化样式 */
        .content {
            background-color: #f5f7fa;
            padding: 20px;
        }

        /* 卡片样式美化 */
        .card {
            background-color: #fff;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);
            margin-bottom: 20px;
            transition: all 0.3s;
        }

        .card:hover {
            box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
            transform: translateY(-2px);
        }

        .card-header {
            padding: 16px 20px;
            border-bottom: 1px solid #f0f0f0;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .card-title {
            font-size: 16px;
            font-weight: 600;
            color: #333;
            margin: 0;
        }

        /* 统计卡片样式 */
        .grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(220px, 1fr));
            gap: 20px;
            margin-bottom: 20px;
        }

        .stat-card {
            padding: 20px;
            display: flex;
            flex-direction: column;
            position: relative;
            overflow: hidden;
        }

        .stat-card::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 4px;
            background: linear-gradient(90deg, #1890ff, #52c41a);
        }

        .stat-label {
            font-size: 14px;
            color: #666;
            margin-bottom: 10px;
        }

        .stat-value {
            font-size: 24px;
            font-weight: 600;
            color: #333;
            margin-bottom: 10px;
        }

        /* 表格样式美化 */
        .table {
            width: 100%;
            border-collapse: separate;
            border-spacing: 0;
        }

        .table th {
            background-color: #fafafa;
            padding: 12px 16px;
            font-weight: 600;
            color: #333;
            border-bottom: 1px solid #f0f0f0;
            text-align: left;
        }

        .table td {
            padding: 12px 16px;
            border-bottom: 1px solid #f0f0f0;
        }

        .table tr:hover {
            background-color: #f5f7fa;
        }

        /* 按钮样式美化 */
        .btn {
            padding: 8px 16px;
            border-radius: 4px;
            font-size: 14px;
            font-weight: 500;
            cursor: pointer;
            transition: all 0.2s;
            border: none;
        }

        .btn-primary {
            background-color: #1890ff;
            color: white;
        }

        .btn-primary:hover {
            background-color: #40a9ff;
            box-shadow: 0 2px 8px rgba(24, 144, 255, 0.3);
        }

        /* 图表容器样式 */
        .chart-container {
            padding: 20px;
            height: 300px;
        }

        /* 下拉选择框样式 */
        .form-control {
            padding: 8px 12px;
            border: 1px solid #d9d9d9;
            border-radius: 4px;
            font-size: 14px;
            color: #333;
            background-color: white;
            transition: all 0.3s;
        }

        .form-control:hover, .form-control:focus {
            border-color: #40a9ff;
            box-shadow: 0 0 0 2px rgba(24, 144, 255, 0.2);
            outline: none;
        }
    </style>
</head>
<body>
    <div class="container">
        <!-- 顶部栏 -->
        <div class="topbar">
            <div class="topbar-left">
                <div class="toggle-sidebar" onclick="toggleSidebar()">
                    <i class="fas fa-bars"></i>
                </div>
                <img src="logo.png" alt="公司Logo" class="topbar-logo">
                <h2 class="topbar-title">精准曝气控制系统</h2>
            </div>
            <div class="topbar-right">
                <div class="user-dropdown">
                    <div class="user-info">
                        <div class="user-avatar">管</div>
                        <span>管理员</span>
                        <i class="fas fa-chevron-down" style="margin-left: 5px; font-size: 12px;"></i>
                    </div>
                    <div class="dropdown-content">
                        <a href="#"><i class="fas fa-user-circle"></i> 个人信息</a>
                        <a href="#"><i class="fas fa-cog"></i> 账号设置</a>
                        <a href="index.html" onclick="logout()"><i class="fas fa-sign-out-alt"></i> 退出登录</a>
                    </div>
                </div>
            </div>
        </div>

        <!-- 主体内容区域 -->
        <div class="main-container">
            <!-- 侧边栏 -->
            <div class="sidebar">
                <div class="sidebar-menu">
                    <div class="menu-item active" id="menu-dashboard" onclick="window.location.href='dashboard.html'">
                        <i class="fas fa-tachometer-alt"></i>
                        <span>实时监控</span>
                    </div>
                    <div class="menu-item" id="menu-model" onclick="window.location.href='model-control.html'">
                        <i class="fas fa-brain"></i>
                        <span>模型控制</span>
                    </div>
                    <div class="menu-item" id="menu-scheduling" onclick="window.location.href='scheduling.html'">
                        <i class="fas fa-tasks"></i>
                        <span>调度管理</span>
                    </div>
                    <div class="menu-item" id="menu-reports" onclick="window.location.href='reports.html'">
                        <i class="fas fa-chart-bar"></i>
                        <span>数据报表</span>
                    </div>
                    <div class="menu-item" id="menu-alarms" onclick="window.location.href='alarms.html'">
                        <i class="fas fa-bell"></i>
                        <span>告警管理</span>
                    </div>
                    <div class="menu-item" id="menu-settings" onclick="window.location.href='settings.html'">
                        <i class="fas fa-cog"></i>
                        <span>系统配置</span>
                    </div>
                </div>
            </div>

            <!-- 内容区域 -->
            <div class="content">
                <!-- 状态概览 -->
                <div class="grid">
                    <div class="card stat-card do-card">
                        <div class="stat-icon">
                            <i class="fas fa-water"></i>
                        </div>
                        <div class="stat-content">
                            <div class="stat-label">当前DO浓度</div>
                            <div class="stat-value">2.5 mg/L</div>
                            <div class="stat-status">
                                <div class="status-indicator status-normal"></div>
                                <span>正常</span>
                            </div>
                        </div>
                    </div>
                    <div class="card stat-card nh-card">
                        <div class="stat-icon">
                            <i class="fas fa-flask"></i>
                        </div>
                        <div class="stat-content">
                            <div class="stat-label">氨氮浓度</div>
                            <div class="stat-value">1.2 mg/L</div>
                            <div class="stat-status">
                                <div class="status-indicator status-normal"></div>
                                <span>正常</span>
                            </div>
                        </div>
                    </div>
                    <div class="card stat-card blower-card">
                        <div class="stat-icon">
                            <i class="fas fa-fan"></i>
                        </div>
                        <div class="stat-content">
                            <div class="stat-label">鼓风机状态</div>
                            <div class="stat-value">运行中</div>
                            <div class="stat-status">
                                <div class="status-indicator status-normal"></div>
                                <span>正常</span>
                            </div>
                        </div>
                    </div>
                    <div class="card stat-card energy-card">
                        <div class="stat-icon">
                            <i class="fas fa-bolt"></i>
                        </div>
                        <div class="stat-content">
                            <div class="stat-label">当前能耗</div>
                            <div class="stat-value">45.2 kWh</div>
                            <div class="stat-status">
                                <div class="status-indicator status-warning"></div>
                                <span>偏高</span>
                            </div>
                        </div>
                    </div>
                </div>

                <style>
                    /* 统计卡片额外样式 */
                    .stat-card {
                        display: flex;
                        align-items: center;
                        padding: 20px;
                    }

                    .stat-icon {
                        width: 50px;
                        height: 50px;
                        border-radius: 10px;
                        display: flex;
                        align-items: center;
                        justify-content: center;
                        margin-right: 15px;
                        font-size: 24px;
                        color: white;
                    }

                    .do-card .stat-icon {
                        background-color: #1890ff;
                    }

                    .nh-card .stat-icon {
                        background-color: #52c41a;
                    }

                    .blower-card .stat-icon {
                        background-color: #722ed1;
                    }

                    .energy-card .stat-icon {
                        background-color: #fa8c16;
                    }

                    .stat-content {
                        flex: 1;
                    }

                    .stat-status {
                        display: flex;
                        align-items: center;
                        margin-top: 5px;
                        font-size: 14px;
                        color: #666;
                    }
                </style>

                <!-- DO浓度趋势图 -->
                <div class="card">
                    <div class="card-header">
                        <div class="card-title">
                            <i class="fas fa-chart-line" style="color: #1890ff; margin-right: 8px;"></i>
                            DO浓度与能耗趋势
                        </div>
                        <div class="card-actions">
                            <div class="time-selector">
                                <span>时间范围：</span>
                                <select id="timeRange" class="form-control">
                                    <option value="1">最近1小时</option>
                                    <option value="6">最近6小时</option>
                                    <option value="24" selected>最近24小时</option>
                                </select>
                            </div>
                        </div>
                    </div>
                    <div class="chart-wrapper">
                        <div class="chart-container" id="doChart"></div>
                        <div class="chart-overlay">
                            <div class="chart-loading" style="display: none;">
                                <i class="fas fa-spinner fa-spin"></i>
                                <span>加载中...</span>
                            </div>
                        </div>
                    </div>

                    <style>
                        /* 图表容器美化 */
                        .chart-wrapper {
                            position: relative;
                            padding: 0 20px 20px;
                        }

                        .chart-container {
                            height: 350px;  /* 增加图表高度 */
                            position: relative;
                            z-index: 1;
                        }

                        .chart-overlay {
                            position: absolute;
                            top: 0;
                            left: 0;
                            right: 0;
                            bottom: 0;
                            display: flex;
                            align-items: center;
                            justify-content: center;
                            z-index: 2;
                            pointer-events: none;
                        }

                        .chart-loading {
                            background-color: rgba(255, 255, 255, 0.8);
                            padding: 15px 25px;
                            border-radius: 6px;
                            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
                            display: flex;
                            align-items: center;
                            gap: 10px;
                            color: #1890ff;
                            font-weight: 500;
                        }

                        .chart-loading i {
                            font-size: 18px;
                        }
                    </style>
                </div>

                <!-- 曝气区域状态表格 -->
                <div class="card">
                    <div class="card-header">
                        <div class="card-title">
                            <i class="fas fa-table" style="color: #722ed1; margin-right: 8px;"></i>
                            曝气区域状态
                        </div>
                        <div class="card-actions">
                            <button class="btn btn-outline" onclick="refreshZoneStatus()">
                                <i class="fas fa-sync-alt"></i> 刷新数据
                            </button>
                        </div>
                    </div>
                    <div class="table-responsive">
                        <table class="table">
                            <thead>
                                <tr>
                                    <th>区域</th>
                                    <th>DO浓度</th>
                                    <th>阀门开度</th>
                                    <th>状态</th>
                                    <th>操作</th>
                                </tr>
                            </thead>
                            <tbody>
                                <tr>
                                    <td><strong>曝气区域1</strong></td>
                                    <td>2.3 mg/L</td>
                                    <td>
                                        <div class="progress-bar">
                                            <div class="progress-fill" style="width: 65%;"></div>
                                            <span>65%</span>
                                        </div>
                                    </td>
                                    <td><div class="status-indicator status-normal"></div> 正常</td>
                                    <td>
                                        <button class="btn btn-primary" onclick="showZoneDetails(1)">
                                            <i class="fas fa-info-circle"></i> 详情
                                        </button>
                                    </td>
                                </tr>
                                <tr>
                                    <td><strong>曝气区域2</strong></td>
                                    <td>1.8 mg/L</td>
                                    <td>
                                        <div class="progress-bar">
                                            <div class="progress-fill" style="width: 45%;"></div>
                                            <span>45%</span>
                                        </div>
                                    </td>
                                    <td><div class="status-indicator status-normal"></div> 正常</td>
                                    <td>
                                        <button class="btn btn-primary" onclick="showZoneDetails(2)">
                                            <i class="fas fa-info-circle"></i> 详情
                                        </button>
                                    </td>
                                </tr>
                                <tr>
                                    <td><strong>曝气区域3</strong></td>
                                    <td>3.2 mg/L</td>
                                    <td>
                                        <div class="progress-bar">
                                            <div class="progress-fill warning" style="width: 80%;"></div>
                                            <span>80%</span>
                                        </div>
                                    </td>
                                    <td><div class="status-indicator status-warning"></div> 偏高</td>
                                    <td>
                                        <button class="btn btn-primary" onclick="showZoneDetails(3)">
                                            <i class="fas fa-info-circle"></i> 详情
                                        </button>
                                    </td>
                                </tr>
                                <tr>
                                    <td><strong>曝气区域4</strong></td>
                                    <td>2.1 mg/L</td>
                                    <td>
                                        <div class="progress-bar">
                                            <div class="progress-fill" style="width: 55%;"></div>
                                            <span>55%</span>
                                        </div>
                                    </td>
                                    <td><div class="status-indicator status-normal"></div> 正常</td>
                                    <td>
                                        <button class="btn btn-primary" onclick="showZoneDetails(4)">
                                            <i class="fas fa-info-circle"></i> 详情
                                        </button>
                                    </td>
                                </tr>
                            </tbody>
                        </table>
                    </div>
                </div>

                <style>
                    /* 卡片操作区域样式 */
                    .card-actions {
                        display: flex;
                        align-items: center;
                    }

                    .time-selector {
                        display: flex;
                        align-items: center;
                        gap: 8px;
                        color: #666;
                        font-size: 14px;
                    }

                    /* 表格响应式容器 */
                    .table-responsive {
                        overflow-x: auto;
                        padding: 0 20px 20px;
                    }

                    /* 进度条样式 */
                    .progress-bar {
                        width: 100%;
                        height: 8px;
                        background-color: #f0f0f0;
                        border-radius: 4px;
                        overflow: hidden;
                        position: relative;
                        margin-bottom: 4px;
                    }

                    .progress-fill {
                        height: 100%;
                        background-color: #1890ff;
                        border-radius: 4px;
                    }

                    .progress-fill.warning {
                        background-color: #faad14;
                    }

                    /* 轮廓按钮样式 */
                    .btn-outline {
                        background-color: transparent;
                        border: 1px solid #d9d9d9;
                        color: #666;
                        padding: 7px 15px;
                        border-radius: 4px;
                        cursor: pointer;
                        transition: all 0.3s;
                    }

                    .btn-outline:hover {
                        border-color: #1890ff;
                        color: #1890ff;
                    }
                </style>
            </div>
        </div>
    </div>

    <!-- Include Chart.js from Chinese CDN -->
    <script src="js/echarts.min.js"></script>
    <script src="js/common.js"></script>
    <script>
        // Initialize common elements
        loadCommonElements();

        // Show zone details
        function showZoneDetails(zoneId) {
            showNotification(`正在加载曝气区域${zoneId}的详细信息...`, 'info');
            // In a real application, this would fetch data from the backend
        }

        // Refresh zone status
        function refreshZoneStatus() {
            showNotification('正在刷新曝气区域状态数据...', 'info');

            // 模拟加载延迟
            setTimeout(function() {
                // 在实际应用中，这里会从后端获取最新数据
                showNotification('曝气区域状态数据已更新', 'success');
            }, 1000);
        }

        // Initialize DO chart with better static data
        document.addEventListener('DOMContentLoaded', function() {
            const ctx = document.getElementById('doChart').getContext('2d');

            // 使用更多数据点来创建更平滑的曲线
            const timeLabels = [
                '00:00', '01:00', '02:00', '03:00', '04:00', '05:00', '06:00', '07:00',
                '08:00', '09:00', '10:00', '11:00', '12:00', '13:00', '14:00', '15:00',
                '16:00', '17:00', '18:00', '19:00', '20:00', '21:00', '22:00', '23:00'
            ];

            // 实际DO浓度 - 模拟一天中的变化趋势，更多数据点使曲线更平滑
            const doData = [
                2.1, 2.0, 1.8, 1.7, 1.6, 1.7, 1.9, 2.1,
                2.3, 2.5, 2.8, 3.0, 3.1, 3.0, 2.9, 2.8,
                2.7, 2.6, 2.5, 2.4, 2.3, 2.2, 2.2, 2.1
            ];

            // 设定值 - 更平滑的波动
            const setpointData = [
                2.0, 2.0, 2.0, 2.0, 2.0, 2.0, 2.0, 2.1,
                2.2, 2.2, 2.2, 2.2, 2.2, 2.2, 2.2, 2.1,
                2.0, 2.0, 2.0, 2.0, 2.0, 2.0, 2.0, 2.0
            ];

            // 添加第三条线 - 能耗数据，更多数据点
            const energyData = [
                42, 40, 38, 36, 35, 36, 37, 41,
                45, 48, 52, 55, 58, 57, 55, 53,
                50, 49, 48, 47, 45, 44, 43, 42
            ];

            // 创建渐变背景
            const gradientBg1 = ctx.createLinearGradient(0, 0, 0, 300);
            gradientBg1.addColorStop(0, 'rgba(24, 144, 255, 0.3)');
            gradientBg1.addColorStop(1, 'rgba(24, 144, 255, 0)');

            const gradientBg2 = ctx.createLinearGradient(0, 0, 0, 300);
            gradientBg2.addColorStop(0, 'rgba(250, 140, 22, 0.2)');
            gradientBg2.addColorStop(1, 'rgba(250, 140, 22, 0)');

            const chart = new Chart(ctx, {
                type: 'line',
                data: {
                    labels: timeLabels,
                    datasets: [
                        {
                            label: '实际DO浓度',
                            data: doData,
                            borderColor: '#1890ff',
                            backgroundColor: gradientBg1,
                            fill: true,
                            tension: 0.4,  // 曲线平滑度
                            borderWidth: 3,
                            pointRadius: 0,  // 隐藏所有点
                            pointHoverRadius: 6,  // 悬停时显示点
                            pointBackgroundColor: '#fff',
                            pointHoverBackgroundColor: '#1890ff',
                            pointBorderColor: '#1890ff',
                            pointHoverBorderColor: '#fff',
                            pointBorderWidth: 2,
                            pointHoverBorderWidth: 2,
                            yAxisID: 'y'
                        },
                        {
                            label: '设定值',
                            data: setpointData,
                            borderColor: '#52c41a',
                            borderDash: [5, 5],
                            fill: false,
                            tension: 0.4,
                            borderWidth: 2,
                            pointRadius: 0,
                            pointHoverRadius: 4,
                            yAxisID: 'y'
                        },
                        {
                            label: '能耗 (kWh)',
                            data: energyData,
                            borderColor: '#fa8c16',
                            backgroundColor: gradientBg2,
                            fill: true,
                            tension: 0.4,
                            borderWidth: 3,
                            pointRadius: 0,
                            pointHoverRadius: 6,
                            pointBackgroundColor: '#fff',
                            pointHoverBackgroundColor: '#fa8c16',
                            pointBorderColor: '#fa8c16',
                            pointHoverBorderColor: '#fff',
                            pointBorderWidth: 2,
                            pointHoverBorderWidth: 2,
                            yAxisID: 'y1'
                        }
                    ]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    animation: {
                        duration: 2000,  // 动画持续时间
                        easing: 'easeOutQuart'  // 缓动函数
                    },
                    interaction: {
                        mode: 'index',
                        intersect: false
                    },
                    plugins: {
                        legend: {
                            position: 'top',
                            align: 'center',
                            labels: {
                                usePointStyle: true,
                                boxWidth: 10,
                                padding: 20,
                                font: {
                                    size: 12,
                                    family: "'Helvetica Neue', 'Helvetica', 'Arial', sans-serif"
                                }
                            }
                        },
                        tooltip: {
                            enabled: true,
                            mode: 'index',
                            intersect: false,
                            backgroundColor: 'rgba(255, 255, 255, 0.95)',
                            titleColor: '#333',
                            bodyColor: '#666',
                            borderColor: '#e8e8e8',
                            borderWidth: 1,
                            padding: 12,
                            boxPadding: 6,
                            cornerRadius: 6,
                            titleFont: {
                                size: 14,
                                weight: 'bold'
                            },
                            bodyFont: {
                                size: 13
                            },
                            callbacks: {
                                // 自定义提示框标题
                                title: function(tooltipItems) {
                                    return '时间: ' + tooltipItems[0].label;
                                },
                                // 自定义提示框内容
                                label: function(context) {
                                    let label = context.dataset.label || '';
                                    if (label) {
                                        label += ': ';
                                    }
                                    if (context.parsed.y !== null) {
                                        if (context.datasetIndex === 0 || context.datasetIndex === 1) {
                                            label += context.parsed.y.toFixed(2) + ' mg/L';
                                        } else {
                                            label += context.parsed.y.toFixed(1) + ' kWh';
                                        }
                                    }
                                    return label;
                                }
                            }
                        }
                    },
                    scales: {
                        x: {
                            grid: {
                                color: 'rgba(0, 0, 0, 0.03)',
                                drawBorder: false
                            },
                            ticks: {
                                color: '#666',
                                font: {
                                    size: 11
                                },
                                maxRotation: 0,  // 防止标签旋转
                                autoSkip: true,  // 自动跳过标签以避免拥挤
                                maxTicksLimit: 12  // 最大显示标签数量
                            }
                        },
                        y: {
                            type: 'linear',
                            display: true,
                            position: 'left',
                            min: 0,
                            max: 4,  // 稍微降低最大值，使曲线更明显
                            title: {
                                display: true,
                                text: 'DO浓度 (mg/L)',
                                color: '#1890ff',
                                font: {
                                    size: 12,
                                    weight: 'bold'
                                },
                                padding: {
                                    bottom: 10
                                }
                            },
                            grid: {
                                color: 'rgba(0, 0, 0, 0.03)',
                                drawBorder: false
                            },
                            ticks: {
                                color: '#666',
                                font: {
                                    size: 11
                                },
                                padding: 8,
                                stepSize: 0.5  // 固定刻度间隔
                            }
                        },
                        y1: {
                            type: 'linear',
                            display: true,
                            position: 'right',
                            min: 30,  // 调整最小值，使曲线更明显
                            max: 65,  // 调整最大值，使曲线更明显
                            title: {
                                display: true,
                                text: '能耗 (kWh)',
                                color: '#fa8c16',
                                font: {
                                    size: 12,
                                    weight: 'bold'
                                },
                                padding: {
                                    bottom: 10
                                }
                            },
                            grid: {
                                drawOnChartArea: false,
                                drawBorder: false
                            },
                            ticks: {
                                color: '#666',
                                font: {
                                    size: 11
                                },
                                padding: 8,
                                stepSize: 5  // 固定刻度间隔
                            }
                        }
                    },
                    // 添加插件以绘制曲线下方的渐变区域
                    plugins: [{
                        beforeDraw: function(chart) {
                            const ctx = chart.ctx;
                            const chartArea = chart.chartArea;

                            // 清除画布
                            ctx.save();
                            ctx.globalCompositeOperation = 'destination-over';

                            // 绘制背景
                            ctx.fillStyle = '#f9fafc';
                            ctx.fillRect(chartArea.left, chartArea.top, chartArea.width, chartArea.height);

                            ctx.restore();
                        }
                    }]
                }
            });

            // 更新图表数据 - 根据时间范围选择
            document.getElementById('timeRange').addEventListener('change', function() {
                const hours = parseInt(this.value);
                let newLabels, newDoData, newSetpointData, newEnergyData;

                // 显示加载状态
                showNotification('正在加载数据...', 'info');

                // 显示图表加载动画
                document.querySelector('.chart-loading').style.display = 'flex';

                // 根据选择的时间范围提供不同的数据集
                if (hours === 1) {
                    // 最近1小时的数据 - 5分钟间隔
                    newLabels = [
                        '21:00', '21:05', '21:10', '21:15', '21:20', '21:25',
                        '21:30', '21:35', '21:40', '21:45', '21:50', '21:55'
                    ];

                    // 更平滑的DO浓度曲线
                    newDoData = [
                        2.20, 2.25, 2.35, 2.40, 2.35, 2.25,
                        2.15, 2.00, 1.95, 2.05, 2.15, 2.25
                    ];

                    // 设定值
                    newSetpointData = [
                        2.0, 2.0, 2.0, 2.0, 2.0, 2.0,
                        2.0, 2.0, 2.0, 2.0, 2.0, 2.0
                    ];

                    // 能耗数据
                    newEnergyData = [
                        43.0, 43.5, 44.2, 44.8, 44.3, 43.5,
                        42.8, 41.5, 40.8, 41.2, 42.0, 42.8
                    ];
                } else if (hours === 6) {
                    // 最近6小时的数据 - 30分钟间隔
                    newLabels = [
                        '16:00', '16:30', '17:00', '17:30', '18:00', '18:30',
                        '19:00', '19:30', '20:00', '20:30', '21:00', '21:30'
                    ];

                    // 更平滑的DO浓度曲线
                    newDoData = [
                        2.70, 2.65, 2.55, 2.45, 2.50, 2.45,
                        2.35, 2.25, 2.30, 2.25, 2.20, 2.25
                    ];

                    // 设定值
                    newSetpointData = [
                        2.2, 2.2, 2.2, 2.0, 2.0, 2.0,
                        2.0, 2.0, 2.0, 2.0, 2.0, 2.0
                    ];

                    // 能耗数据
                    newEnergyData = [
                        50.5, 49.8, 48.5, 47.2, 48.0, 47.5,
                        46.8, 45.5, 45.2, 44.5, 43.8, 44.2
                    ];
                } else {
                    // 最近24小时的数据 - 每小时间隔
                    newLabels = timeLabels;
                    newDoData = doData;
                    newSetpointData = setpointData;
                    newEnergyData = energyData;
                }

                // 模拟网络延迟
                setTimeout(function() {
                    // 更新图表数据
                    chart.data.labels = newLabels;
                    chart.data.datasets[0].data = newDoData;
                    chart.data.datasets[1].data = newSetpointData;
                    chart.data.datasets[2].data = newEnergyData;

                    // 使用动画更新图表
                    chart.update({
                        duration: 1000,
                        easing: 'easeOutQuad'
                    });

                    // 隐藏图表加载动画
                    document.querySelector('.chart-loading').style.display = 'none';

                    // 显示成功通知
                    showNotification('数据已更新', 'success');
                }, 500);

                // 更新图表标题
                const timeRangeText = this.options[this.selectedIndex].text;
                document.querySelector('.card-title').innerHTML =
                    `<i class="fas fa-chart-line" style="color: #1890ff; margin-right: 8px;"></i>
                     DO浓度与能耗趋势 <span style="font-size: 14px; color: #666; font-weight: normal; margin-left: 8px;">(${timeRangeText})</span>`;
            });
        });
    </script>
</body>
</html>
