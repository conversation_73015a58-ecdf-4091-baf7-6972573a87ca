<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>精准曝气控制系统 (GPAC) - 模型控制</title>
    <link rel="stylesheet" href="css/all.min.css">
    <link rel="stylesheet" href="css/style.css">
    <style>
        /* 确保内容不被侧边栏遮挡 */
        .content {
            margin-left: var(--sidebar-width) !important;
            width: calc(100% - var(--sidebar-width)) !important;
            padding: 20px !important;
            box-sizing: border-box !important;
        }

        .model-card {
            border: 1px solid var(--border-color);
            border-radius: 4px;
            padding: 15px;
            margin-bottom: 15px;
        }
        .model-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 10px;
        }
        .model-title {
            font-weight: 500;
            font-size: 16px;
        }
        .model-status {
            display: flex;
            align-items: center;
        }
        .model-status-indicator {
            width: 10px;
            height: 10px;
            border-radius: 50%;
            margin-right: 5px;
        }
        .model-status-active {
            background-color: var(--secondary-color);
        }
        .model-status-inactive {
            background-color: var(--text-light);
        }
        .model-body {
            margin-bottom: 15px;
        }
        .model-footer {
            display: flex;
            justify-content: flex-end;
        }
        .parameter-table {
            width: 100%;
            border-collapse: collapse;
            margin-bottom: 15px;
        }
        .parameter-table th, .parameter-table td {
            padding: 8px 10px;
            text-align: left;
            border-bottom: 1px solid var(--border-color);
        }
        .parameter-table th {
            background-color: #fafafa;
            font-weight: 500;
        }
        .parameter-input {
            width: 100%;
            padding: 5px 8px;
            border: 1px solid var(--border-color);
            border-radius: 4px;
        }
        .tabs {
            display: flex;
            border-bottom: 1px solid var(--border-color);
            margin-bottom: 20px;
        }
        .tab {
            padding: 10px 15px;
            cursor: pointer;
            border-bottom: 2px solid transparent;
        }
        .tab.active {
            border-bottom-color: var(--primary-color);
            color: var(--primary-color);
        }
        .tab-content {
            display: none;
        }
        .tab-content.active {
            display: block;
        }
    </style>
</head>
<body>
<script>document.addEventListener("DOMContentLoaded", function() { loadCommonElements(); });</script>
    <div class="container">
        <!-- 顶部栏 -->
        <div class="topbar">
            <div class="topbar-left">
                <div class="toggle-sidebar" onclick="toggleSidebar()">
                    <i class="fas fa-bars"></i>
                </div>
                <img src="logo.png" alt="公司Logo" class="topbar-logo" style="height: 60px; margin-right: 15px; transition: transform 0.3s;" onmouseover="this.style.transform='scale(1.05)'" onmouseout="this.style.transform='scale(1)'">
                <h2 class="topbar-title">精准曝气控制系统</h2>
            </div>
            <div class="topbar-right">
                <div class="user-dropdown">
                    <div class="user-info">
                        <div class="user-avatar">管</div>
                        <span>管理员</span>
                        <i class="fas fa-chevron-down" style="margin-left: 5px; font-size: 12px;"></i>
                    </div>
                    <div class="dropdown-content">
                        <a href="#"><i class="fas fa-user-circle"></i> 个人信息</a>
                        <a href="#"><i class="fas fa-cog"></i> 账号设置</a>
                        <a href="index.html" onclick="logout()"><i class="fas fa-sign-out-alt"></i> 退出登录</a>
                    </div>
                </div>
            </div>
        </div>

        <!-- 主体内容区域 -->
        <div class="main-container">
            <!-- 侧边栏 -->
            <div class="sidebar">
                <div class="sidebar-menu">
                    <div class="menu-item" id="menu-dashboard" onclick="window.location.href='dashboard.html'">
                        <i class="fas fa-tachometer-alt"></i>
                        <span>实时监控</span>
                    </div>
                    <div class="menu-item active" id="menu-model" onclick="window.location.href='model-control.html'">
                        <i class="fas fa-brain"></i>
                        <span>模型控制</span>
                    </div>
                    <div class="menu-item" id="menu-scheduling" onclick="window.location.href='scheduling.html'">
                        <i class="fas fa-tasks"></i>
                        <span>调度管理</span>
                    </div>
                    <div class="menu-item" id="menu-reports" onclick="window.location.href='reports.html'">
                        <i class="fas fa-chart-bar"></i>
                        <span>数据报表</span>
                    </div>
                    <div class="menu-item" id="menu-alarms" onclick="window.location.href='alarms.html'">
                        <i class="fas fa-bell"></i>
                        <span>告警管理</span>
                    </div>
                    <div class="menu-item" id="menu-settings" onclick="window.location.href='settings.html'">
                        <i class="fas fa-cog"></i>
                        <span>系统配置</span>
                    </div>
                </div>
            </div>

            <!-- 内容区域 -->
            <div class="content">
                <div class="card" style="width: 100%; box-sizing: border-box;">
                    <div class="card-header">
                        <div class="card-title">模型控制面板</div>
                    </div>

                    <div class="tabs">
                        <div class="tab active" onclick="switchTab('model-config')">模型配置</div>
                        <div class="tab" onclick="switchTab('model-calibration')">模型校准</div>
                        <div class="tab" onclick="switchTab('prediction-settings')">预测设置</div>
                    </div>

                    <!-- Model Configuration Tab -->
                    <div id="model-config" class="tab-content active">
                        <div class="model-card">
                            <div class="model-header">
                                <div class="model-title">氨氮预测模型</div>
                                <div class="model-status">
                                    <div class="model-status-indicator model-status-active"></div>
                                    <span>已启用</span>
                                </div>
                            </div>
                            <div class="model-body">
                                <p>基于历史数据和实时监测值，预测未来氨氮浓度变化趋势。</p>
                                <table class="parameter-table">
                                    <thead>
                                        <tr>
                                            <th>参数名称</th>
                                            <th>当前值</th>
                                            <th>单位</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <tr>
                                            <td>预测时间窗口</td>
                                            <td><input type="number" class="parameter-input" value="60" min="10" max="120"></td>
                                            <td>分钟</td>
                                        </tr>
                                        <tr>
                                            <td>采样频率</td>
                                            <td><input type="number" class="parameter-input" value="5" min="1" max="30"></td>
                                            <td>分钟</td>
                                        </tr>
                                        <tr>
                                            <td>模型置信度阈值</td>
                                            <td><input type="number" class="parameter-input" value="85" min="50" max="99"></td>
                                            <td>%</td>
                                        </tr>
                                    </tbody>
                                </table>
                            </div>
                            <div class="model-footer">
                                <button class="btn btn-primary" onclick="saveModelConfig('nh4')">保存配置</button>
                            </div>
                        </div>

                        <div class="model-card">
                            <div class="model-header">
                                <div class="model-title">DO变化趋势模型</div>
                                <div class="model-status">
                                    <div class="model-status-indicator model-status-active"></div>
                                    <span>已启用</span>
                                </div>
                            </div>
                            <div class="model-body">
                                <p>基于曝气量、水温等因素，预测DO浓度变化趋势。</p>
                                <table class="parameter-table">
                                    <thead>
                                        <tr>
                                            <th>参数名称</th>
                                            <th>当前值</th>
                                            <th>单位</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <tr>
                                            <td>预测时间窗口</td>
                                            <td><input type="number" class="parameter-input" value="30" min="5" max="60"></td>
                                            <td>分钟</td>
                                        </tr>
                                        <tr>
                                            <td>采样频率</td>
                                            <td><input type="number" class="parameter-input" value="1" min="0.5" max="10" step="0.5"></td>
                                            <td>分钟</td>
                                        </tr>
                                        <tr>
                                            <td>模型灵敏度</td>
                                            <td>
                                                <select class="parameter-input">
                                                    <option value="high">高</option>
                                                    <option value="medium" selected>中</option>
                                                    <option value="low">低</option>
                                                </select>
                                            </td>
                                            <td>-</td>
                                        </tr>
                                    </tbody>
                                </table>
                            </div>
                            <div class="model-footer">
                                <button class="btn btn-primary" onclick="saveModelConfig('do')">保存配置</button>
                            </div>
                        </div>

                        <div class="model-card">
                            <div class="model-header">
                                <div class="model-title">鼓风机总气量预测模型</div>
                                <div class="model-status">
                                    <div class="model-status-indicator model-status-active"></div>
                                    <span>已启用</span>
                                </div>
                            </div>
                            <div class="model-body">
                                <p>基于历史运行数据和工艺参数，预测鼓风机总气量需求变化趋势。</p>
                                <table class="parameter-table">
                                    <thead>
                                        <tr>
                                            <th>参数名称</th>
                                            <th>当前值</th>
                                            <th>单位</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <tr>
                                            <td>预测时间窗口</td>
                                            <td><input type="number" class="parameter-input" value="45" min="10" max="120"></td>
                                            <td>分钟</td>
                                        </tr>
                                        <tr>
                                            <td>采样频率</td>
                                            <td><input type="number" class="parameter-input" value="3" min="1" max="15"></td>
                                            <td>分钟</td>
                                        </tr>
                                        <tr>
                                            <td>模型置信度阈值</td>
                                            <td><input type="number" class="parameter-input" value="80" min="50" max="99"></td>
                                            <td>%</td>
                                        </tr>
                                    </tbody>
                                </table>
                            </div>
                            <div class="model-footer">
                                <button class="btn btn-primary" onclick="saveModelConfig('blower')">保存配置</button>
                            </div>
                        </div>

                        <div class="model-card">
                            <div class="model-header">
                                <div class="model-title">控制区域气量预测模型</div>
                                <div class="model-status">
                                    <div class="model-status-indicator model-status-active"></div>
                                    <span>已启用</span>
                                </div>
                            </div>
                            <div class="model-body">
                                <p>基于区域水质参数和负荷变化，预测各控制区域所需气量。</p>
                                <table class="parameter-table">
                                    <thead>
                                        <tr>
                                            <th>参数名称</th>
                                            <th>当前值</th>
                                            <th>单位</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <tr>
                                            <td>预测时间窗口</td>
                                            <td><input type="number" class="parameter-input" value="40" min="10" max="90"></td>
                                            <td>分钟</td>
                                        </tr>
                                        <tr>
                                            <td>采样频率</td>
                                            <td><input type="number" class="parameter-input" value="2" min="1" max="10"></td>
                                            <td>分钟</td>
                                        </tr>
                                        <tr>
                                            <td>区域划分精度</td>
                                            <td>
                                                <select class="parameter-input">
                                                    <option value="high">高</option>
                                                    <option value="medium" selected>中</option>
                                                    <option value="low">低</option>
                                                </select>
                                            </td>
                                            <td>-</td>
                                        </tr>
                                    </tbody>
                                </table>
                            </div>
                            <div class="model-footer">
                                <button class="btn btn-primary" onclick="saveModelConfig('area')">保存配置</button>
                            </div>
                        </div>

                        <div class="model-card">
                            <div class="model-header">
                                <div class="model-title">空气调节阀门预测模型</div>
                                <div class="model-status">
                                    <div class="model-status-indicator model-status-active"></div>
                                    <span>已启用</span>
                                </div>
                            </div>
                            <div class="model-body">
                                <p>基于气量需求和系统压力，预测阀门开度的最优控制策略。</p>
                                <table class="parameter-table">
                                    <thead>
                                        <tr>
                                            <th>参数名称</th>
                                            <th>当前值</th>
                                            <th>单位</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <tr>
                                            <td>预测时间窗口</td>
                                            <td><input type="number" class="parameter-input" value="20" min="5" max="60"></td>
                                            <td>分钟</td>
                                        </tr>
                                        <tr>
                                            <td>控制精度</td>
                                            <td><input type="number" class="parameter-input" value="2" min="1" max="5"></td>
                                            <td>%</td>
                                        </tr>
                                        <tr>
                                            <td>响应速度</td>
                                            <td>
                                                <select class="parameter-input">
                                                    <option value="fast">快速</option>
                                                    <option value="normal" selected>正常</option>
                                                    <option value="slow">缓慢</option>
                                                </select>
                                            </td>
                                            <td>-</td>
                                        </tr>
                                    </tbody>
                                </table>
                            </div>
                            <div class="model-footer">
                                <button class="btn btn-primary" onclick="saveModelConfig('valve')">保存配置</button>
                            </div>
                        </div>
                    </div>

                    <!-- Model Calibration Tab -->
                    <div id="model-calibration" class="tab-content">
                        <div class="card-header">
                            <div class="card-title">模型校准</div>
                        </div>
                        <p>使用历史数据对模型进行校准，提高预测精度。</p>

                        <div class="form-group">
                            <label class="form-label">选择模型</label>
                            <select class="form-control">
                                <option value="nh4">氨氮预测模型</option>
                                <option value="do">DO变化趋势模型</option>
                                <option value="load">污泥负荷模型</option>
                                <option value="blower">鼓风机总气量预测模型</option>
                                <option value="area">控制区域气量预测模型</option>
                                <option value="valve">空气调节阀门预测模型</option>
                            </select>
                        </div>

                        <div class="form-group">
                            <label class="form-label">校准数据范围</label>
                            <div style="display: flex; gap: 10px;">
                                <input type="date" class="form-control" value="2023-05-01">
                                <span style="line-height: 36px;">至</span>
                                <input type="date" class="form-control" value="2023-05-15">
                            </div>
                        </div>

                        <div class="form-group">
                            <label class="form-label">校准方法</label>
                            <select class="form-control">
                                <option value="auto">自动校准</option>
                                <option value="manual">手动校准</option>
                                <option value="hybrid">混合校准</option>
                            </select>
                        </div>

                        <div class="form-group">
                            <button class="btn btn-primary" onclick="startCalibration()">开始校准</button>
                        </div>
                    </div>

                    <!-- Prediction Settings Tab -->
                    <div id="prediction-settings" class="tab-content">
                        <div class="card-header">
                            <div class="card-title">预测设置</div>
                        </div>

                        <div class="form-group">
                            <label class="form-label">MPC控制周期</label>
                            <select class="form-control">
                                <option value="1">1分钟</option>
                                <option value="5" selected>5分钟</option>
                                <option value="10">10分钟</option>
                                <option value="15">15分钟</option>
                            </select>
                        </div>

                        <div class="form-group">
                            <label class="form-label">DO设定值</label>
                            <input type="number" class="form-control" value="2.0" min="0.5" max="5.0" step="0.1">
                            <small>单位: mg/L</small>
                        </div>

                        <div class="form-group">
                            <label class="form-label">氨氮控制目标</label>
                            <input type="number" class="form-control" value="1.5" min="0.1" max="10.0" step="0.1">
                            <small>单位: mg/L</small>
                        </div>

                        <div class="form-group">
                            <label class="form-label">控制优先级</label>
                            <select class="form-control">
                                <option value="energy">节能优先</option>
                                <option value="quality" selected>水质优先</option>
                                <option value="balanced">平衡模式</option>
                            </select>
                        </div>

                        <div class="form-group">
                            <button class="btn btn-primary" onclick="savePredictionSettings()">保存设置</button>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="js/common.js"></script>
    <script>
        // Tab functionality

        // Switch tabs
        function switchTab(tabId) {
            // Hide all tab contents
            const tabContents = document.querySelectorAll('.tab-content');
            tabContents.forEach(content => {
                content.classList.remove('active');
            });

            // Deactivate all tabs
            const tabs = document.querySelectorAll('.tab');
            tabs.forEach(tab => {
                tab.classList.remove('active');
            });

            // Activate selected tab and content
            document.getElementById(tabId).classList.add('active');
            const selectedTab = document.querySelector(`.tab[onclick="switchTab('${tabId}')"]`);
            selectedTab.classList.add('active');
        }

        // Save model configuration
        function saveModelConfig(modelType) {
            let modelName = '';
            switch(modelType) {
                case 'nh4':
                    modelName = '氨氮预测模型';
                    break;
                case 'do':
                    modelName = 'DO变化趋势模型';
                    break;
                case 'blower':
                    modelName = '鼓风机总气量预测模型';
                    break;
                case 'area':
                    modelName = '控制区域气量预测模型';
                    break;
                case 'valve':
                    modelName = '空气调节阀门预测模型';
                    break;
                default:
                    modelName = '未知模型';
            }
            showNotification(`${modelName}配置已保存`, 'success');
        }

        // Start model calibration
        function startCalibration() {
            showNotification('模型校准已启动，请等待校准完成', 'info');
        }

        // Save prediction settings
        function savePredictionSettings() {
            showNotification('预测设置已保存', 'success');
        }

        // Show notification function
        function showNotification(message, type = 'info') {
            const notification = document.createElement('div');
            notification.className = `notification ${type}`;
            notification.innerHTML = `
                <div class="notification-content">
                    <i class="fas ${type === 'success' ? 'fa-check-circle' : type === 'error' ? 'fa-times-circle' : 'fa-info-circle'}"></i>
                    <span>${message}</span>
                </div>
            `;

            document.body.appendChild(notification);

            // Show notification
            setTimeout(() => {
                notification.classList.add('show');
            }, 10);

            // Hide and remove notification after 3 seconds
            setTimeout(() => {
                notification.classList.remove('show');
                setTimeout(() => {
                    document.body.removeChild(notification);
                }, 300);
            }, 3000);
        }
    </script>
</body>
</html>
