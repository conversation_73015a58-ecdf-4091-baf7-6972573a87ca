<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>精准曝气控制系统 (GPAC) - 调度计划管理</title>
    <link rel="stylesheet" href="css/all.min.css">
    <link rel="stylesheet" href="css/style.css">
    <style>
        /* 子菜单样式 */
        .submenu {
            display: flex;
            flex-wrap: wrap;
            gap: 10px;
            margin-bottom: 20px;
            background-color: #fff;
            padding: 15px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);
        }

        .submenu-item {
            padding: 8px 16px;
            border-radius: 6px;
            cursor: pointer;
            transition: all 0.3s;
            display: flex;
            align-items: center;
            gap: 8px;
            font-size: 14px;
            color: #666;
        }

        .submenu-item:hover {
            background-color: #f5f7fa;
            color: var(--primary-color);
        }

        .submenu-item.active {
            background-color: var(--primary-color);
            color: white;
        }

        /* 标签页样式 */
        .tab-container {
            background-color: #fff;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);
            margin-bottom: 20px;
            overflow: hidden;
        }

        .tab-header {
            display: flex;
            border-bottom: 1px solid #f0f0f0;
            background-color: #fafafa;
        }

        .tab-item {
            padding: 15px 20px;
            cursor: pointer;
            font-weight: 500;
            color: #666;
            transition: all 0.3s;
            position: relative;
        }

        .tab-item:hover {
            color: var(--primary-color);
        }

        .tab-item.active {
            color: var(--primary-color);
        }

        .tab-item.active::after {
            content: '';
            position: absolute;
            bottom: 0;
            left: 0;
            width: 100%;
            height: 2px;
            background-color: var(--primary-color);
        }

        .tab-content {
            padding: 20px;
            display: none;
        }

        .tab-content.active {
            display: block;
        }

        /* 表单样式 */
        .form-section {
            margin-bottom: 25px;
            padding-bottom: 20px;
            border-bottom: 1px solid #f0f0f0;
        }

        .form-section:last-child {
            border-bottom: none;
            margin-bottom: 0;
            padding-bottom: 0;
        }

        .section-title {
            font-size: 16px;
            font-weight: 600;
            margin-bottom: 15px;
            color: #333;
            display: flex;
            align-items: center;
            gap: 8px;
        }

        .form-row {
            display: flex;
            flex-wrap: wrap;
            gap: 20px;
            margin-bottom: 15px;
        }

        .form-group {
            flex: 1;
            min-width: 250px;
        }

        .form-label {
            display: block;
            margin-bottom: 8px;
            font-size: 14px;
            color: #666;
        }

        .form-control {
            width: 100%;
            padding: 10px 12px;
            border: 1px solid #d9d9d9;
            border-radius: 4px;
            font-size: 14px;
            transition: all 0.3s;
        }

        .form-control:hover {
            border-color: #40a9ff;
        }

        .form-control:focus {
            border-color: #1890ff;
            box-shadow: 0 0 0 2px rgba(24, 144, 255, 0.2);
            outline: none;
        }

        .form-text {
            font-size: 12px;
            color: #999;
            margin-top: 5px;
        }

        /* 开关样式 */
        .switch {
            position: relative;
            display: inline-block;
            width: 44px;
            height: 22px;
        }

        .switch input {
            opacity: 0;
            width: 0;
            height: 0;
        }

        .slider {
            position: absolute;
            cursor: pointer;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background-color: #ccc;
            transition: .4s;
            border-radius: 22px;
        }

        .slider:before {
            position: absolute;
            content: "";
            height: 18px;
            width: 18px;
            left: 3px;
            bottom: 2px;
            background-color: white;
            transition: .4s;
            border-radius: 50%;
        }

        input:checked + .slider {
            background-color: var(--primary-color);
        }

        input:focus + .slider {
            box-shadow: 0 0 1px var(--primary-color);
        }

        input:checked + .slider:before {
            transform: translateX(20px);
        }

        /* 表格样式 */
        .table-container {
            overflow-x: auto;
            margin-bottom: 20px;
        }

        .data-table {
            width: 100%;
            border-collapse: collapse;
        }

        .data-table th {
            background-color: #fafafa;
            padding: 12px 15px;
            text-align: left;
            font-weight: 500;
            color: #333;
            border-bottom: 1px solid #f0f0f0;
        }

        .data-table td {
            padding: 12px 15px;
            border-bottom: 1px solid #f0f0f0;
        }

        .data-table tr:hover {
            background-color: #f5f7fa;
        }

        /* 按钮样式 */
        .btn-group {
            display: flex;
            gap: 10px;
            margin-top: 20px;
            justify-content: flex-end;
        }

        .btn {
            padding: 8px 16px;
            border-radius: 4px;
            font-size: 14px;
            font-weight: 500;
            cursor: pointer;
            transition: all 0.3s;
            border: none;
        }

        .btn-primary {
            background-color: var(--primary-color);
            color: white;
        }

        .btn-primary:hover {
            background-color: #40a9ff;
            box-shadow: 0 2px 8px rgba(24, 144, 255, 0.3);
        }

        .btn-secondary {
            background-color: #f0f0f0;
            color: #666;
        }

        .btn-secondary:hover {
            background-color: #e0e0e0;
        }

        .btn-danger {
            background-color: #ff4d4f;
            color: white;
        }

        .btn-danger:hover {
            background-color: #ff7875;
            box-shadow: 0 2px 8px rgba(255, 77, 79, 0.3);
        }

        /* 模板卡片样式 */
        .template-cards {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(280px, 1fr));
            gap: 20px;
            margin-bottom: 20px;
        }

        .template-card {
            background-color: #fff;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);
            padding: 15px;
            transition: all 0.3s;
            border: 1px solid #f0f0f0;
            cursor: pointer;
        }

        .template-card:hover {
            box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
            transform: translateY(-2px);
            border-color: var(--primary-color);
        }

        .template-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 15px;
            padding-bottom: 10px;
            border-bottom: 1px solid #f0f0f0;
        }

        .template-name {
            font-weight: 600;
            color: #333;
            display: flex;
            align-items: center;
            gap: 8px;
        }

        .template-type {
            display: inline-block;
            padding: 2px 8px;
            border-radius: 10px;
            font-size: 12px;
            background-color: #e6f7ff;
            color: #1890ff;
            border: 1px solid #91d5ff;
        }

        .template-type.regular {
            background-color: #f6ffed;
            color: #52c41a;
            border: 1px solid #b7eb8f;
        }

        .template-type.temporary {
            background-color: #fff7e6;
            color: #fa8c16;
            border: 1px solid #ffd591;
        }

        .template-type.emergency {
            background-color: #fff1f0;
            color: #f5222d;
            border: 1px solid #ffa39e;
        }

        .template-desc {
            font-size: 14px;
            color: #666;
            margin-bottom: 15px;
            display: -webkit-box;
            -webkit-line-clamp: 2;
            -webkit-box-orient: vertical;
            overflow: hidden;
            text-overflow: ellipsis;
        }

        .template-actions {
            display: flex;
            justify-content: flex-end;
            gap: 10px;
        }

        /* 响应式调整 */
        @media (max-width: 768px) {
            .form-row {
                flex-direction: column;
                gap: 15px;
            }

            .form-group {
                min-width: 100%;
            }

            .submenu {
                overflow-x: auto;
                padding: 10px;
                flex-wrap: nowrap;
            }

            .submenu-item {
                white-space: nowrap;
            }

            .template-cards {
                grid-template-columns: 1fr;
            }
        }
    </style>
</head>
<body>
<script>document.addEventListener("DOMContentLoaded", function() { loadCommonElements(); });</script>
    <div class="container">
        <!-- 顶部栏 -->
        <div class="topbar">
            <div class="topbar-left">
                <div class="toggle-sidebar" onclick="toggleSidebar()">
                    <i class="fas fa-bars"></i>
                </div>
                <img src="logo.png" alt="公司Logo" class="topbar-logo" style="height: 60px; margin-right: 15px; transition: transform 0.3s;" onmouseover="this.style.transform='scale(1.05)'" onmouseout="this.style.transform='scale(1)'">
                <h2 class="topbar-title">精准曝气控制系统</h2>
            </div>
            <div class="topbar-right">
                <div class="user-dropdown">
                    <div class="user-info">
                        <div class="user-avatar">管</div>
                        <span>管理员</span>
                        <i class="fas fa-chevron-down" style="margin-left: 5px; font-size: 12px;"></i>
                    </div>
                    <div class="dropdown-content">
                        <a href="#"><i class="fas fa-user-circle"></i> 个人信息</a>
                        <a href="#"><i class="fas fa-cog"></i> 账号设置</a>
                        <a href="index.html" onclick="logout()"><i class="fas fa-sign-out-alt"></i> 退出登录</a>
                    </div>
                </div>
            </div>
        </div>

        <!-- 主体内容区域 -->
        <div class="main-container">
            <!-- 侧边栏 -->
            <div class="sidebar">
                <div class="sidebar-menu">
                    <div class="menu-item" id="menu-dashboard" onclick="window.location.href='dashboard.html'">
                        <i class="fas fa-tachometer-alt"></i>
                        <span>实时监控</span>
                    </div>
                    <div class="menu-item" id="menu-model" onclick="window.location.href='model-control.html'">
                        <i class="fas fa-brain"></i>
                        <span>模型控制</span>
                    </div>
                    <div class="menu-item active" id="menu-scheduling" onclick="window.location.href='scheduling.html'">
                        <i class="fas fa-tasks"></i>
                        <span>调度管理</span>
                    </div>
                    <div class="menu-item" id="menu-reports" onclick="window.location.href='reports.html'">
                        <i class="fas fa-chart-bar"></i>
                        <span>数据报表</span>
                    </div>
                    <div class="menu-item" id="menu-alarms" onclick="window.location.href='alarms.html'">
                        <i class="fas fa-bell"></i>
                        <span>告警管理</span>
                    </div>
                    <div class="menu-item" id="menu-settings" onclick="window.location.href='settings.html'">
                        <i class="fas fa-cog"></i>
                        <span>系统配置</span>
                    </div>
                </div>
            </div>

            <!-- 内容区域 -->
            <div class="content" style="margin-left: var(--sidebar-width); width: calc(100% - var(--sidebar-width)); padding: 20px;">
                <!-- 子菜单导航 -->
                <div class="submenu">
                    <div class="submenu-item" onclick="window.location.href='scheduling.html'">
                        <i class="fas fa-home"></i> 调度总览
                    </div>
                    <div class="submenu-item" onclick="window.location.href='device-scheduling.html'">
                        <i class="fas fa-fan"></i> 曝气设备调度
                    </div>
                    <div class="submenu-item" onclick="window.location.href='do-parameter-scheduling.html'">
                        <i class="fas fa-sliders-h"></i> DO控制参数调度
                    </div>
                    <div class="submenu-item" onclick="window.location.href='energy-optimization.html'">
                        <i class="fas fa-bolt"></i> 能源优化调度
                    </div>
                    <div class="submenu-item active" onclick="window.location.href='plan-management.html'">
                        <i class="fas fa-calendar-alt"></i> 调度计划管理
                    </div>
                    <div class="submenu-item" onclick="window.location.href='execution-monitoring.html'">
                        <i class="fas fa-desktop"></i> 执行监控
                    </div>
                    <div class="submenu-item" onclick="window.location.href='statistics-reports.html'">
                        <i class="fas fa-chart-pie"></i> 统计报表
                    </div>
                </div>

                <!-- 页面标题 -->
                <div class="page-header">
                    <h1 class="page-title">
                        <i class="fas fa-calendar-alt" style="color: var(--primary-color);"></i>
                        调度计划管理
                    </h1>
                    <div class="page-actions">
                        <button class="btn btn-primary" onclick="createNewPlan()">
                            <i class="fas fa-plus"></i> 创建新计划
                        </button>
                    </div>
                </div>

                <!-- 标签页容器 -->
                <div class="tab-container">
                    <div class="tab-header">
                        <div class="tab-item active" data-tab="plan-create">
                            <i class="fas fa-edit"></i> 计划创建与编辑
                        </div>
                        <div class="tab-item" data-tab="plan-template">
                            <i class="fas fa-copy"></i> 计划模板
                        </div>
                        <div class="tab-item" data-tab="plan-status">
                            <i class="fas fa-toggle-on"></i> 计划启用/禁用
                        </div>
                    </div>

                    <!-- 计划创建与编辑 -->
                    <div class="tab-content active" id="plan-create">
                        <!-- 基本信息设置 -->
                        <div class="form-section">
                            <h3 class="section-title">
                                <i class="fas fa-info-circle" style="color: #1890ff;"></i>
                                基本信息设置
                            </h3>
                            <div class="form-row">
                                <div class="form-group">
                                    <label class="form-label">计划名称</label>
                                    <input type="text" class="form-control" placeholder="输入计划名称">
                                </div>
                                <div class="form-group">
                                    <label class="form-label">计划编号</label>
                                    <input type="text" class="form-control" placeholder="系统自动生成" disabled>
                                </div>
                            </div>

                            <div class="form-row">
                                <div class="form-group">
                                    <label class="form-label">计划描述</label>
                                    <textarea class="form-control" rows="3" placeholder="输入计划描述"></textarea>
                                </div>
                            </div>

                            <div class="form-row">
                                <div class="form-group">
                                    <label class="form-label">有效期</label>
                                    <div style="display: flex; align-items: center; gap: 10px;">
                                        <input type="date" class="form-control" value="2025-05-15">
                                        <span>至</span>
                                        <input type="date" class="form-control" value="2025-12-31">
                                    </div>
                                </div>
                                <div class="form-group">
                                    <label class="form-label">创建人</label>
                                    <input type="text" class="form-control" value="管理员" disabled>
                                </div>
                            </div>
                        </div>

                        <!-- 计划类型选择 -->
                        <div class="form-section">
                            <h3 class="section-title">
                                <i class="fas fa-tag" style="color: #52c41a;"></i>
                                计划类型选择
                            </h3>
                            <div class="form-row">
                                <div class="form-group">
                                    <label class="form-label">计划类型</label>
                                    <select class="form-control" id="plan-type">
                                        <option value="regular" selected>常规计划</option>
                                        <option value="temporary">临时计划</option>
                                        <option value="emergency">应急计划</option>
                                    </select>
                                </div>
                                <div class="form-group">
                                    <label class="form-label">优先级</label>
                                    <select class="form-control">
                                        <option value="low">低</option>
                                        <option value="medium" selected>中</option>
                                        <option value="high">高</option>
                                        <option value="urgent">紧急</option>
                                    </select>
                                </div>
                            </div>

                            <div class="form-row">
                                <div class="form-group">
                                    <label class="form-label">适用场景</label>
                                    <select class="form-control" multiple style="height: 100px;">
                                        <option selected>正常运行</option>
                                        <option>高负荷运行</option>
                                        <option>低负荷运行</option>
                                        <option>设备维护</option>
                                        <option>节能运行</option>
                                        <option>应急处理</option>
                                    </select>
                                    <div class="form-text">按住Ctrl键可多选</div>
                                </div>
                                <div class="form-group">
                                    <label class="form-label">执行周期</label>
                                    <select class="form-control" id="execution-cycle">
                                        <option value="once">一次性执行</option>
                                        <option value="daily" selected>每日执行</option>
                                        <option value="weekly">每周执行</option>
                                        <option value="monthly">每月执行</option>
                                        <option value="custom">自定义</option>
                                    </select>
                                    <div id="custom-cycle" style="display: none; margin-top: 10px;">
                                        <input type="text" class="form-control" placeholder="输入自定义周期表达式">
                                        <div class="form-text">例如: 0 0 8 * * ? (每天8点执行)</div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- 计划内容配置 -->
                        <div class="form-section">
                            <h3 class="section-title">
                                <i class="fas fa-cogs" style="color: #fa8c16;"></i>
                                计划内容配置
                            </h3>
                            <div class="form-row">
                                <div class="form-group">
                                    <label class="form-label">调度类型</label>
                                    <select class="form-control" id="schedule-type">
                                        <option value="device">曝气设备调度</option>
                                        <option value="do-param" selected>DO控制参数调度</option>
                                        <option value="energy">能源优化调度</option>
                                        <option value="combined">组合调度</option>
                                    </select>
                                </div>
                            </div>

                            <!-- DO控制参数调度配置 -->
                            <div id="do-param-config">
                                <div class="form-row">
                                    <div class="form-group">
                                        <label class="form-label">DO目标值设置</label>
                                        <div class="table-container">
                                            <table class="data-table">
                                                <thead>
                                                    <tr>
                                                        <th>时段</th>
                                                        <th>时间范围</th>
                                                        <th>DO目标值 (mg/L)</th>
                                                        <th>操作</th>
                                                    </tr>
                                                </thead>
                                                <tbody>
                                                    <tr>
                                                        <td>早晨</td>
                                                        <td>06:00 - 12:00</td>
                                                        <td>
                                                            <input type="number" class="form-control" value="2.5" min="0" max="10" step="0.1" style="width: 80px;">
                                                        </td>
                                                        <td>
                                                            <button class="btn btn-secondary" style="padding: 4px 8px;">
                                                                <i class="fas fa-edit"></i>
                                                            </button>
                                                            <button class="btn btn-danger" style="padding: 4px 8px;">
                                                                <i class="fas fa-trash"></i>
                                                            </button>
                                                        </td>
                                                    </tr>
                                                    <tr>
                                                        <td>中午</td>
                                                        <td>12:00 - 18:00</td>
                                                        <td>
                                                            <input type="number" class="form-control" value="3.0" min="0" max="10" step="0.1" style="width: 80px;">
                                                        </td>
                                                        <td>
                                                            <button class="btn btn-secondary" style="padding: 4px 8px;">
                                                                <i class="fas fa-edit"></i>
                                                            </button>
                                                            <button class="btn btn-danger" style="padding: 4px 8px;">
                                                                <i class="fas fa-trash"></i>
                                                            </button>
                                                        </td>
                                                    </tr>
                                                    <tr>
                                                        <td>晚上</td>
                                                        <td>18:00 - 06:00</td>
                                                        <td>
                                                            <input type="number" class="form-control" value="1.8" min="0" max="10" step="0.1" style="width: 80px;">
                                                        </td>
                                                        <td>
                                                            <button class="btn btn-secondary" style="padding: 4px 8px;">
                                                                <i class="fas fa-edit"></i>
                                                            </button>
                                                            <button class="btn btn-danger" style="padding: 4px 8px;">
                                                                <i class="fas fa-trash"></i>
                                                            </button>
                                                        </td>
                                                    </tr>
                                                </tbody>
                                            </table>
                                        </div>
                                        <button class="btn btn-secondary" style="margin-top: 10px;">
                                            <i class="fas fa-plus"></i> 添加时段
                                        </button>
                                    </div>
                                </div>

                                <div class="form-row">
                                    <div class="form-group">
                                        <label class="form-label">PID参数设置</label>
                                        <div style="display: flex; gap: 15px;">
                                            <div>
                                                <label class="form-label">比例系数 (P)</label>
                                                <input type="number" class="form-control" value="0.5" step="0.1">
                                            </div>
                                            <div>
                                                <label class="form-label">积分系数 (I)</label>
                                                <input type="number" class="form-control" value="0.2" step="0.1">
                                            </div>
                                            <div>
                                                <label class="form-label">微分系数 (D)</label>
                                                <input type="number" class="form-control" value="0.1" step="0.1">
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- 计划验证 -->
                        <div class="form-section">
                            <h3 class="section-title">
                                <i class="fas fa-check-circle" style="color: #722ed1;"></i>
                                计划验证
                            </h3>
                            <div class="form-row">
                                <div class="form-group">
                                    <label class="form-label">验证状态</label>
                                    <div style="display: flex; align-items: center; gap: 10px; color: #52c41a;">
                                        <i class="fas fa-check-circle"></i>
                                        <span>计划验证通过</span>
                                    </div>
                                </div>
                                <div class="form-group">
                                    <button class="btn btn-primary">
                                        <i class="fas fa-play-circle"></i> 运行验证
                                    </button>
                                </div>
                            </div>

                            <div class="form-row">
                                <div class="form-group">
                                    <label class="form-label">验证结果</label>
                                    <div class="table-container">
                                        <table class="data-table">
                                            <thead>
                                                <tr>
                                                    <th>验证项</th>
                                                    <th>结果</th>
                                                    <th>详情</th>
                                                </tr>
                                            </thead>
                                            <tbody>
                                                <tr>
                                                    <td>参数有效性</td>
                                                    <td><span style="color: #52c41a;"><i class="fas fa-check-circle"></i> 通过</span></td>
                                                    <td>所有参数在有效范围内</td>
                                                </tr>
                                                <tr>
                                                    <td>时间冲突</td>
                                                    <td><span style="color: #52c41a;"><i class="fas fa-check-circle"></i> 通过</span></td>
                                                    <td>无时间冲突</td>
                                                </tr>
                                                <tr>
                                                    <td>资源可用性</td>
                                                    <td><span style="color: #52c41a;"><i class="fas fa-check-circle"></i> 通过</span></td>
                                                    <td>所需资源可用</td>
                                                </tr>
                                                <tr>
                                                    <td>安全性检查</td>
                                                    <td><span style="color: #52c41a;"><i class="fas fa-check-circle"></i> 通过</span></td>
                                                    <td>无安全风险</td>
                                                </tr>
                                            </tbody>
                                        </table>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <div class="btn-group">
                            <button class="btn btn-secondary">取消</button>
                            <button class="btn btn-primary" onclick="savePlan()">保存计划</button>
                        </div>
                    </div>

                    <!-- 计划模板标签页 -->
                    <div class="tab-content" id="plan-template">
                        <!-- 模板库 -->
                        <div class="form-section">
                            <h3 class="section-title">
                                <i class="fas fa-book" style="color: #1890ff;"></i>
                                模板库
                            </h3>
                            <div class="form-row">
                                <div class="form-group">
                                    <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 15px;">
                                        <div style="display: flex; gap: 10px;">
                                            <input type="text" class="form-control" placeholder="搜索模板..." style="width: 250px;">
                                            <button class="btn btn-secondary">
                                                <i class="fas fa-search"></i> 搜索
                                            </button>
                                        </div>
                                        <div>
                                            <button class="btn btn-primary">
                                                <i class="fas fa-plus"></i> 新建模板
                                            </button>
                                        </div>
                                    </div>

                                    <div class="template-cards">
                                        <div class="template-card">
                                            <div class="template-header">
                                                <div class="template-name">
                                                    <i class="fas fa-file-alt"></i> 标准DO控制模板
                                                </div>
                                                <div class="template-type regular">常规</div>
                                            </div>
                                            <div class="template-desc">
                                                适用于正常运行工况下的DO控制参数设置，包含三个时段的DO目标值和PID参数。
                                            </div>
                                            <div class="template-actions">
                                                <button class="btn btn-primary" onclick="useTemplate(1)">
                                                    <i class="fas fa-check"></i> 使用
                                                </button>
                                                <button class="btn btn-secondary" onclick="viewTemplate(1)">
                                                    <i class="fas fa-eye"></i> 查看
                                                </button>
                                            </div>
                                        </div>

                                        <div class="template-card">
                                            <div class="template-header">
                                                <div class="template-name">
                                                    <i class="fas fa-file-alt"></i> 节能运行模板
                                                </div>
                                                <div class="template-type regular">常规</div>
                                            </div>
                                            <div class="template-desc">
                                                针对低负荷工况设计的节能运行模板，降低DO目标值，优化PID参数，减少能耗。
                                            </div>
                                            <div class="template-actions">
                                                <button class="btn btn-primary" onclick="useTemplate(2)">
                                                    <i class="fas fa-check"></i> 使用
                                                </button>
                                                <button class="btn btn-secondary" onclick="viewTemplate(2)">
                                                    <i class="fas fa-eye"></i> 查看
                                                </button>
                                            </div>
                                        </div>

                                        <div class="template-card">
                                            <div class="template-header">
                                                <div class="template-name">
                                                    <i class="fas fa-file-alt"></i> 高负荷应对模板
                                                </div>
                                                <div class="template-type temporary">临时</div>
                                            </div>
                                            <div class="template-desc">
                                                适用于进水水质突变或高负荷工况，提高DO目标值，加强曝气强度，确保处理效果。
                                            </div>
                                            <div class="template-actions">
                                                <button class="btn btn-primary" onclick="useTemplate(3)">
                                                    <i class="fas fa-check"></i> 使用
                                                </button>
                                                <button class="btn btn-secondary" onclick="viewTemplate(3)">
                                                    <i class="fas fa-eye"></i> 查看
                                                </button>
                                            </div>
                                        </div>

                                        <div class="template-card">
                                            <div class="template-header">
                                                <div class="template-name">
                                                    <i class="fas fa-file-alt"></i> 设备维护模式
                                                </div>
                                                <div class="template-type temporary">临时</div>
                                            </div>
                                            <div class="template-desc">
                                                设备维护期间使用的特殊运行模式，调整运行参数以适应部分设备离线的情况。
                                            </div>
                                            <div class="template-actions">
                                                <button class="btn btn-primary" onclick="useTemplate(4)">
                                                    <i class="fas fa-check"></i> 使用
                                                </button>
                                                <button class="btn btn-secondary" onclick="viewTemplate(4)">
                                                    <i class="fas fa-eye"></i> 查看
                                                </button>
                                            </div>
                                        </div>

                                        <div class="template-card">
                                            <div class="template-header">
                                                <div class="template-name">
                                                    <i class="fas fa-file-alt"></i> 应急处理模板
                                                </div>
                                                <div class="template-type emergency">应急</div>
                                            </div>
                                            <div class="template-desc">
                                                应对突发情况的应急处理模板，最大化曝气能力，确保系统稳定性和出水水质。
                                            </div>
                                            <div class="template-actions">
                                                <button class="btn btn-primary" onclick="useTemplate(5)">
                                                    <i class="fas fa-check"></i> 使用
                                                </button>
                                                <button class="btn btn-secondary" onclick="viewTemplate(5)">
                                                    <i class="fas fa-eye"></i> 查看
                                                </button>
                                            </div>
                                        </div>

                                        <div class="template-card">
                                            <div class="template-header">
                                                <div class="template-name">
                                                    <i class="fas fa-file-alt"></i> 峰谷电价响应模板
                                                </div>
                                                <div class="template-type regular">常规</div>
                                            </div>
                                            <div class="template-desc">
                                                根据峰谷电价时段自动调整运行参数，在电价低谷时段提高DO目标值，峰时段降低。
                                            </div>
                                            <div class="template-actions">
                                                <button class="btn btn-primary" onclick="useTemplate(6)">
                                                    <i class="fas fa-check"></i> 使用
                                                </button>
                                                <button class="btn btn-secondary" onclick="viewTemplate(6)">
                                                    <i class="fas fa-eye"></i> 查看
                                                </button>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- 模板使用 -->
                        <div class="form-section">
                            <h3 class="section-title">
                                <i class="fas fa-magic" style="color: #52c41a;"></i>
                                模板使用
                            </h3>
                            <div class="form-row">
                                <div class="form-group">
                                    <label class="form-label">选择模板</label>
                                    <select class="form-control">
                                        <option>-- 请选择模板 --</option>
                                        <option>标准DO控制模板</option>
                                        <option>节能运行模板</option>
                                        <option>高负荷应对模板</option>
                                        <option>设备维护模式</option>
                                        <option>应急处理模板</option>
                                        <option>峰谷电价响应模板</option>
                                    </select>
                                </div>
                                <div class="form-group">
                                    <label class="form-label">新计划名称</label>
                                    <input type="text" class="form-control" placeholder="输入新计划名称">
                                </div>
                            </div>

                            <div class="form-row">
                                <div class="form-group">
                                    <label class="form-label">参数调整</label>
                                    <div style="display: flex; align-items: center; gap: 10px;">
                                        <label class="switch">
                                            <input type="checkbox" checked>
                                            <span class="slider"></span>
                                        </label>
                                        <span>允许在创建计划时调整模板参数</span>
                                    </div>
                                </div>
                                <div class="form-group">
                                    <button class="btn btn-primary" style="margin-top: 24px;">
                                        <i class="fas fa-plus-circle"></i> 基于模板创建计划
                                    </button>
                                </div>
                            </div>
                        </div>

                        <!-- 模板保存 -->
                        <div class="form-section">
                            <h3 class="section-title">
                                <i class="fas fa-save" style="color: #fa8c16;"></i>
                                模板保存
                            </h3>
                            <div class="form-row">
                                <div class="form-group">
                                    <label class="form-label">选择计划</label>
                                    <select class="form-control">
                                        <option>-- 请选择计划 --</option>
                                        <option>工作日标准运行计划</option>
                                        <option>周末节能运行计划</option>
                                        <option>夏季高温应对计划</option>
                                        <option>冬季低温运行计划</option>
                                        <option>设备检修期间运行计划</option>
                                    </select>
                                </div>
                                <div class="form-group">
                                    <label class="form-label">模板名称</label>
                                    <input type="text" class="form-control" placeholder="输入模板名称">
                                </div>
                            </div>

                            <div class="form-row">
                                <div class="form-group">
                                    <label class="form-label">模板描述</label>
                                    <textarea class="form-control" rows="3" placeholder="输入模板描述"></textarea>
                                </div>
                            </div>

                            <div class="form-row">
                                <div class="form-group">
                                    <label class="form-label">模板类型</label>
                                    <select class="form-control">
                                        <option value="regular" selected>常规模板</option>
                                        <option value="temporary">临时模板</option>
                                        <option value="emergency">应急模板</option>
                                    </select>
                                </div>
                                <div class="form-group">
                                    <button class="btn btn-primary" style="margin-top: 24px;">
                                        <i class="fas fa-save"></i> 保存为模板
                                    </button>
                                </div>
                            </div>
                        </div>

                        <!-- 模板管理 -->
                        <div class="form-section">
                            <h3 class="section-title">
                                <i class="fas fa-cog" style="color: #722ed1;"></i>
                                模板管理
                            </h3>
                            <div class="table-container">
                                <table class="data-table">
                                    <thead>
                                        <tr>
                                            <th>模板名称</th>
                                            <th>类型</th>
                                            <th>创建时间</th>
                                            <th>使用次数</th>
                                            <th>操作</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <tr>
                                            <td>标准DO控制模板</td>
                                            <td><span class="template-type regular">常规</span></td>
                                            <td>2025-01-15</td>
                                            <td>42</td>
                                            <td>
                                                <button class="btn btn-secondary" style="padding: 4px 8px;">
                                                    <i class="fas fa-edit"></i>
                                                </button>
                                                <button class="btn btn-secondary" style="padding: 4px 8px;">
                                                    <i class="fas fa-copy"></i>
                                                </button>
                                                <button class="btn btn-danger" style="padding: 4px 8px;">
                                                    <i class="fas fa-trash"></i>
                                                </button>
                                            </td>
                                        </tr>
                                        <tr>
                                            <td>节能运行模板</td>
                                            <td><span class="template-type regular">常规</span></td>
                                            <td>2025-02-20</td>
                                            <td>28</td>
                                            <td>
                                                <button class="btn btn-secondary" style="padding: 4px 8px;">
                                                    <i class="fas fa-edit"></i>
                                                </button>
                                                <button class="btn btn-secondary" style="padding: 4px 8px;">
                                                    <i class="fas fa-copy"></i>
                                                </button>
                                                <button class="btn btn-danger" style="padding: 4px 8px;">
                                                    <i class="fas fa-trash"></i>
                                                </button>
                                            </td>
                                        </tr>
                                        <tr>
                                            <td>高负荷应对模板</td>
                                            <td><span class="template-type temporary">临时</span></td>
                                            <td>2025-03-10</td>
                                            <td>15</td>
                                            <td>
                                                <button class="btn btn-secondary" style="padding: 4px 8px;">
                                                    <i class="fas fa-edit"></i>
                                                </button>
                                                <button class="btn btn-secondary" style="padding: 4px 8px;">
                                                    <i class="fas fa-copy"></i>
                                                </button>
                                                <button class="btn btn-danger" style="padding: 4px 8px;">
                                                    <i class="fas fa-trash"></i>
                                                </button>
                                            </td>
                                        </tr>
                                        <tr>
                                            <td>设备维护模式</td>
                                            <td><span class="template-type temporary">临时</span></td>
                                            <td>2025-04-05</td>
                                            <td>8</td>
                                            <td>
                                                <button class="btn btn-secondary" style="padding: 4px 8px;">
                                                    <i class="fas fa-edit"></i>
                                                </button>
                                                <button class="btn btn-secondary" style="padding: 4px 8px;">
                                                    <i class="fas fa-copy"></i>
                                                </button>
                                                <button class="btn btn-danger" style="padding: 4px 8px;">
                                                    <i class="fas fa-trash"></i>
                                                </button>
                                            </td>
                                        </tr>
                                        <tr>
                                            <td>应急处理模板</td>
                                            <td><span class="template-type emergency">应急</span></td>
                                            <td>2025-01-30</td>
                                            <td>3</td>
                                            <td>
                                                <button class="btn btn-secondary" style="padding: 4px 8px;">
                                                    <i class="fas fa-edit"></i>
                                                </button>
                                                <button class="btn btn-secondary" style="padding: 4px 8px;">
                                                    <i class="fas fa-copy"></i>
                                                </button>
                                                <button class="btn btn-danger" style="padding: 4px 8px;">
                                                    <i class="fas fa-trash"></i>
                                                </button>
                                            </td>
                                        </tr>
                                    </tbody>
                                </table>
                            </div>

                            <div style="display: flex; justify-content: space-between; margin-top: 15px;">
                                <div>
                                    <button class="btn btn-secondary">
                                        <i class="fas fa-file-export"></i> 导出模板
                                    </button>
                                    <button class="btn btn-secondary">
                                        <i class="fas fa-file-import"></i> 导入模板
                                    </button>
                                </div>
                                <div>
                                    <button class="btn btn-danger">
                                        <i class="fas fa-trash-alt"></i> 批量删除
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- 计划启用/禁用标签页 -->
                    <div class="tab-content" id="plan-status">
                        <!-- 内容将在下一步添加 -->
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="js/common.js"></script>
    <script>
        // 初始化公共元素
        document.addEventListener('DOMContentLoaded', function() {
            // 这里可以添加页面初始化代码
        });

        // 创建新计划
        function createNewPlan() {
            window.location.href = 'plan-create.html';
        }
    </script>
</body>
</html>
