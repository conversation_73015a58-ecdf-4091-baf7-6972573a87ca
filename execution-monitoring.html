<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>精准曝气控制系统 (GPAC) - 执行监控</title>
    <link rel="stylesheet" href="css/all.min.css">
    <link rel="stylesheet" href="css/style.css">
    <style>
        /* 子菜单样式 */
        .submenu {
            display: flex;
            flex-wrap: wrap;
            gap: 10px;
            margin-bottom: 20px;
            background-color: #fff;
            padding: 15px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);
        }

        .submenu-item {
            padding: 8px 16px;
            border-radius: 6px;
            cursor: pointer;
            transition: all 0.3s;
            display: flex;
            align-items: center;
            gap: 8px;
            font-size: 14px;
            color: #666;
        }

        .submenu-item:hover {
            background-color: #f5f7fa;
            color: var(--primary-color);
        }

        .submenu-item.active {
            background-color: var(--primary-color);
            color: white;
        }

        /* 标签页样式 */
        .tab-container {
            background-color: #fff;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);
            margin-bottom: 20px;
            overflow: hidden;
        }

        .tab-header {
            display: flex;
            border-bottom: 1px solid #f0f0f0;
            background-color: #fafafa;
        }

        .tab-item {
            padding: 15px 20px;
            cursor: pointer;
            font-weight: 500;
            color: #666;
            transition: all 0.3s;
            position: relative;
        }

        .tab-item:hover {
            color: var(--primary-color);
        }

        .tab-item.active {
            color: var(--primary-color);
        }

        .tab-item.active::after {
            content: '';
            position: absolute;
            bottom: 0;
            left: 0;
            width: 100%;
            height: 2px;
            background-color: var(--primary-color);
        }

        .tab-content {
            padding: 20px;
            display: none;
        }

        .tab-content.active {
            display: block;
        }

        /* 表单样式 */
        .form-section {
            margin-bottom: 25px;
            padding-bottom: 20px;
            border-bottom: 1px solid #f0f0f0;
        }

        .form-section:last-child {
            border-bottom: none;
            margin-bottom: 0;
            padding-bottom: 0;
        }

        .section-title {
            font-size: 16px;
            font-weight: 600;
            margin-bottom: 15px;
            color: #333;
            display: flex;
            align-items: center;
            gap: 8px;
        }

        .form-row {
            display: flex;
            flex-wrap: wrap;
            gap: 20px;
            margin-bottom: 15px;
        }

        .form-group {
            flex: 1;
            min-width: 250px;
        }

        .form-label {
            display: block;
            margin-bottom: 8px;
            font-size: 14px;
            color: #666;
        }

        .form-control {
            width: 100%;
            padding: 10px 12px;
            border: 1px solid #d9d9d9;
            border-radius: 4px;
            font-size: 14px;
            transition: all 0.3s;
        }

        .form-control:hover {
            border-color: #40a9ff;
        }

        .form-control:focus {
            border-color: #1890ff;
            box-shadow: 0 0 0 2px rgba(24, 144, 255, 0.2);
            outline: none;
        }

        .form-text {
            font-size: 12px;
            color: #999;
            margin-top: 5px;
        }

        /* 表格样式 */
        .table-container {
            overflow-x: auto;
            margin-bottom: 20px;
        }

        .data-table {
            width: 100%;
            border-collapse: collapse;
        }

        .data-table th {
            background-color: #fafafa;
            padding: 12px 15px;
            text-align: left;
            font-weight: 500;
            color: #333;
            border-bottom: 1px solid #f0f0f0;
        }

        .data-table td {
            padding: 12px 15px;
            border-bottom: 1px solid #f0f0f0;
        }

        .data-table tr:hover {
            background-color: #f5f7fa;
        }

        /* 按钮样式 */
        .btn-group {
            display: flex;
            gap: 10px;
            margin-top: 20px;
            justify-content: flex-end;
        }

        .btn {
            padding: 8px 16px;
            border-radius: 4px;
            font-size: 14px;
            font-weight: 500;
            cursor: pointer;
            transition: all 0.3s;
            border: none;
        }

        .btn-primary {
            background-color: var(--primary-color);
            color: white;
        }

        .btn-primary:hover {
            background-color: #40a9ff;
            box-shadow: 0 2px 8px rgba(24, 144, 255, 0.3);
        }

        .btn-secondary {
            background-color: #f0f0f0;
            color: #666;
        }

        .btn-secondary:hover {
            background-color: #e0e0e0;
        }

        .btn-danger {
            background-color: #ff4d4f;
            color: white;
        }

        .btn-danger:hover {
            background-color: #ff7875;
            box-shadow: 0 2px 8px rgba(255, 77, 79, 0.3);
        }

        /* 状态标签样式 */
        .status-tag {
            display: inline-block;
            padding: 2px 8px;
            border-radius: 10px;
            font-size: 12px;
        }

        .status-running {
            background-color: #f6ffed;
            color: #52c41a;
            border: 1px solid #b7eb8f;
        }

        .status-paused {
            background-color: #fff7e6;
            color: #fa8c16;
            border: 1px solid #ffd591;
        }

        .status-completed {
            background-color: #e6f7ff;
            color: #1890ff;
            border: 1px solid #91d5ff;
        }

        .status-failed {
            background-color: #fff1f0;
            color: #f5222d;
            border: 1px solid #ffa39e;
        }

        /* 图表容器样式 */
        .chart-container {
            height: 300px;
            margin-bottom: 20px;
            background-color: #fff;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);
            padding: 15px;
        }

        /* 响应式调整 */
        @media (max-width: 768px) {
            .form-row {
                flex-direction: column;
                gap: 15px;
            }

            .form-group {
                min-width: 100%;
            }

            .submenu {
                overflow-x: auto;
                padding: 10px;
                flex-wrap: nowrap;
            }

            .submenu-item {
                white-space: nowrap;
            }
        }
    </style>
</head>
<body>
<script>document.addEventListener("DOMContentLoaded", function() { loadCommonElements(); });</script>
    <div class="container">
        <!-- 顶部栏 -->
        <div class="topbar">
            <div class="topbar-left">
                <div class="toggle-sidebar" onclick="toggleSidebar()">
                    <i class="fas fa-bars"></i>
                </div>
                <img src="logo.png" alt="公司Logo" class="topbar-logo" style="height: 60px; margin-right: 15px; transition: transform 0.3s;" onmouseover="this.style.transform='scale(1.05)'" onmouseout="this.style.transform='scale(1)'">
                <h2 class="topbar-title">精准曝气控制系统</h2>
            </div>
            <div class="topbar-right">
                <div class="user-dropdown">
                    <div class="user-info">
                        <div class="user-avatar">管</div>
                        <span>管理员</span>
                        <i class="fas fa-chevron-down" style="margin-left: 5px; font-size: 12px;"></i>
                    </div>
                    <div class="dropdown-content">
                        <a href="#"><i class="fas fa-user-circle"></i> 个人信息</a>
                        <a href="#"><i class="fas fa-cog"></i> 账号设置</a>
                        <a href="index.html" onclick="logout()"><i class="fas fa-sign-out-alt"></i> 退出登录</a>
                    </div>
                </div>
            </div>
        </div>

        <!-- 主体内容区域 -->
        <div class="main-container">
            <!-- 侧边栏 -->
            <div class="sidebar">
                <div class="sidebar-menu">
                    <div class="menu-item" id="menu-dashboard" onclick="window.location.href='dashboard.html'">
                        <i class="fas fa-tachometer-alt"></i>
                        <span>实时监控</span>
                    </div>
                    <div class="menu-item" id="menu-model" onclick="window.location.href='model-control.html'">
                        <i class="fas fa-brain"></i>
                        <span>模型控制</span>
                    </div>
                    <div class="menu-item active" id="menu-scheduling" onclick="window.location.href='scheduling.html'">
                        <i class="fas fa-tasks"></i>
                        <span>调度管理</span>
                    </div>
                    <div class="menu-item" id="menu-reports" onclick="window.location.href='reports.html'">
                        <i class="fas fa-chart-bar"></i>
                        <span>数据报表</span>
                    </div>
                    <div class="menu-item" id="menu-alarms" onclick="window.location.href='alarms.html'">
                        <i class="fas fa-bell"></i>
                        <span>告警管理</span>
                    </div>
                    <div class="menu-item" id="menu-settings" onclick="window.location.href='settings.html'">
                        <i class="fas fa-cog"></i>
                        <span>系统配置</span>
                    </div>
                </div>
            </div>

            <!-- 内容区域 -->
            <div class="content" style="margin-left: var(--sidebar-width); width: calc(100% - var(--sidebar-width)); padding: 20px;">
                <!-- 子菜单导航 -->
                <div class="submenu">
                    <div class="submenu-item" onclick="window.location.href='scheduling.html'">
                        <i class="fas fa-home"></i> 调度总览
                    </div>
                    <div class="submenu-item" onclick="window.location.href='device-scheduling.html'">
                        <i class="fas fa-fan"></i> 曝气设备调度
                    </div>
                    <div class="submenu-item" onclick="window.location.href='do-parameter-scheduling.html'">
                        <i class="fas fa-sliders-h"></i> DO控制参数调度
                    </div>
                    <div class="submenu-item" onclick="window.location.href='energy-optimization.html'">
                        <i class="fas fa-bolt"></i> 能源优化调度
                    </div>
                    <div class="submenu-item" onclick="window.location.href='plan-management.html'">
                        <i class="fas fa-calendar-alt"></i> 调度计划管理
                    </div>
                    <div class="submenu-item active" onclick="window.location.href='execution-monitoring.html'">
                        <i class="fas fa-desktop"></i> 执行监控
                    </div>
                    <div class="submenu-item" onclick="window.location.href='statistics-reports.html'">
                        <i class="fas fa-chart-pie"></i> 统计报表
                    </div>
                </div>

                <!-- 页面标题 -->
                <div class="page-header">
                    <h1 class="page-title">
                        <i class="fas fa-desktop" style="color: var(--primary-color);"></i>
                        执行监控
                    </h1>
                    <div class="page-actions">
                        <button class="btn btn-primary" onclick="refreshData()">
                            <i class="fas fa-sync-alt"></i> 刷新数据
                        </button>
                    </div>
                </div>

                <!-- 标签页容器 -->
                <div class="tab-container">
                    <div class="tab-header">
                        <div class="tab-item active" data-tab="execution-status">
                            <i class="fas fa-play-circle"></i> 计划执行状态
                        </div>
                        <div class="tab-item" data-tab="execution-history">
                            <i class="fas fa-history"></i> 执行历史
                        </div>
                        <div class="tab-item" data-tab="manual-intervention">
                            <i class="fas fa-hand-paper"></i> 手动干预
                        </div>
                    </div>

                    <!-- 计划执行状态标签页 -->
                    <div class="tab-content active" id="execution-status">
                        <!-- 状态概览 -->
                        <div class="form-section">
                            <h3 class="section-title">
                                <i class="fas fa-tachometer-alt" style="color: #1890ff;"></i>
                                状态概览
                            </h3>
                            <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 20px; margin-bottom: 20px;">
                                <div style="background-color: #f6ffed; border: 1px solid #b7eb8f; border-radius: 8px; padding: 15px; text-align: center;">
                                    <div style="font-size: 14px; color: #666; margin-bottom: 5px;">运行中计划</div>
                                    <div style="font-size: 24px; font-weight: 600; color: #52c41a;">3</div>
                                </div>
                                <div style="background-color: #fff7e6; border: 1px solid #ffd591; border-radius: 8px; padding: 15px; text-align: center;">
                                    <div style="font-size: 14px; color: #666; margin-bottom: 5px;">暂停计划</div>
                                    <div style="font-size: 24px; font-weight: 600; color: #fa8c16;">1</div>
                                </div>
                                <div style="background-color: #e6f7ff; border: 1px solid #91d5ff; border-radius: 8px; padding: 15px; text-align: center;">
                                    <div style="font-size: 14px; color: #666; margin-bottom: 5px;">待执行计划</div>
                                    <div style="font-size: 24px; font-weight: 600; color: #1890ff;">5</div>
                                </div>
                                <div style="background-color: #fff1f0; border: 1px solid #ffa39e; border-radius: 8px; padding: 15px; text-align: center;">
                                    <div style="font-size: 14px; color: #666; margin-bottom: 5px;">执行异常</div>
                                    <div style="font-size: 24px; font-weight: 600; color: #f5222d;">0</div>
                                </div>
                            </div>

                            <div class="chart-container">
                                <div style="text-align: center; padding: 20px;">
                                    <i class="fas fa-chart-line" style="font-size: 48px; color: #d9d9d9;"></i>
                                    <p>计划执行状态趋势图将在此显示</p>
                                </div>
                            </div>
                        </div>

                        <!-- 当前执行计划 -->
                        <div class="form-section">
                            <h3 class="section-title">
                                <i class="fas fa-play" style="color: #52c41a;"></i>
                                当前执行计划
                            </h3>
                            <div class="form-row">
                                <div class="form-group">
                                    <label class="form-label">筛选条件</label>
                                    <div style="display: flex; gap: 10px;">
                                        <select class="form-control">
                                            <option value="all" selected>所有类型</option>
                                            <option value="device">曝气设备调度</option>
                                            <option value="do-param">DO控制参数调度</option>
                                            <option value="energy">能源优化调度</option>
                                        </select>
                                        <select class="form-control">
                                            <option value="all" selected>所有状态</option>
                                            <option value="running">运行中</option>
                                            <option value="paused">已暂停</option>
                                            <option value="waiting">等待执行</option>
                                            <option value="failed">执行异常</option>
                                        </select>
                                        <button class="btn btn-secondary">
                                            <i class="fas fa-filter"></i> 筛选
                                        </button>
                                    </div>
                                </div>
                            </div>

                            <div class="table-container">
                                <table class="data-table">
                                    <thead>
                                        <tr>
                                            <th>计划名称</th>
                                            <th>类型</th>
                                            <th>开始时间</th>
                                            <th>预计结束时间</th>
                                            <th>执行进度</th>
                                            <th>状态</th>
                                            <th>操作</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <tr>
                                            <td>工作日标准运行计划</td>
                                            <td>DO控制参数调度</td>
                                            <td>2025-05-15 08:00:00</td>
                                            <td>2025-05-15 18:00:00</td>
                                            <td>
                                                <div style="width: 100%; background-color: #f0f0f0; border-radius: 4px;">
                                                    <div style="width: 65%; height: 8px; background-color: #1890ff; border-radius: 4px;"></div>
                                                </div>
                                                <div style="text-align: right; font-size: 12px; color: #666;">65%</div>
                                            </td>
                                            <td><span class="status-tag status-running">运行中</span></td>
                                            <td>
                                                <button class="btn btn-secondary" style="padding: 4px 8px;">
                                                    <i class="fas fa-pause"></i>
                                                </button>
                                                <button class="btn btn-secondary" style="padding: 4px 8px;">
                                                    <i class="fas fa-stop"></i>
                                                </button>
                                                <button class="btn btn-secondary" style="padding: 4px 8px;">
                                                    <i class="fas fa-info-circle"></i>
                                                </button>
                                            </td>
                                        </tr>
                                        <tr>
                                            <td>峰谷电价响应计划</td>
                                            <td>能源优化调度</td>
                                            <td>2025-05-15 06:00:00</td>
                                            <td>2025-05-16 06:00:00</td>
                                            <td>
                                                <div style="width: 100%; background-color: #f0f0f0; border-radius: 4px;">
                                                    <div style="width: 40%; height: 8px; background-color: #1890ff; border-radius: 4px;"></div>
                                                </div>
                                                <div style="text-align: right; font-size: 12px; color: #666;">40%</div>
                                            </td>
                                            <td><span class="status-tag status-running">运行中</span></td>
                                            <td>
                                                <button class="btn btn-secondary" style="padding: 4px 8px;">
                                                    <i class="fas fa-pause"></i>
                                                </button>
                                                <button class="btn btn-secondary" style="padding: 4px 8px;">
                                                    <i class="fas fa-stop"></i>
                                                </button>
                                                <button class="btn btn-secondary" style="padding: 4px 8px;">
                                                    <i class="fas fa-info-circle"></i>
                                                </button>
                                            </td>
                                        </tr>
                                        <tr>
                                            <td>鼓风机轮换计划</td>
                                            <td>曝气设备调度</td>
                                            <td>2025-05-15 08:00:00</td>
                                            <td>2025-05-22 08:00:00</td>
                                            <td>
                                                <div style="width: 100%; background-color: #f0f0f0; border-radius: 4px;">
                                                    <div style="width: 10%; height: 8px; background-color: #1890ff; border-radius: 4px;"></div>
                                                </div>
                                                <div style="text-align: right; font-size: 12px; color: #666;">10%</div>
                                            </td>
                                            <td><span class="status-tag status-running">运行中</span></td>
                                            <td>
                                                <button class="btn btn-secondary" style="padding: 4px 8px;">
                                                    <i class="fas fa-pause"></i>
                                                </button>
                                                <button class="btn btn-secondary" style="padding: 4px 8px;">
                                                    <i class="fas fa-stop"></i>
                                                </button>
                                                <button class="btn btn-secondary" style="padding: 4px 8px;">
                                                    <i class="fas fa-info-circle"></i>
                                                </button>
                                            </td>
                                        </tr>
                                        <tr>
                                            <td>设备维护模式</td>
                                            <td>曝气设备调度</td>
                                            <td>2025-05-14 14:00:00</td>
                                            <td>2025-05-16 14:00:00</td>
                                            <td>
                                                <div style="width: 100%; background-color: #f0f0f0; border-radius: 4px;">
                                                    <div style="width: 50%; height: 8px; background-color: #faad14; border-radius: 4px;"></div>
                                                </div>
                                                <div style="text-align: right; font-size: 12px; color: #666;">50%</div>
                                            </td>
                                            <td><span class="status-tag status-paused">已暂停</span></td>
                                            <td>
                                                <button class="btn btn-secondary" style="padding: 4px 8px;">
                                                    <i class="fas fa-play"></i>
                                                </button>
                                                <button class="btn btn-secondary" style="padding: 4px 8px;">
                                                    <i class="fas fa-stop"></i>
                                                </button>
                                                <button class="btn btn-secondary" style="padding: 4px 8px;">
                                                    <i class="fas fa-info-circle"></i>
                                                </button>
                                            </td>
                                        </tr>
                                    </tbody>
                                </table>
                            </div>
                        </div>

                        <!-- 执行详情 -->
                        <div class="form-section">
                            <h3 class="section-title">
                                <i class="fas fa-list-alt" style="color: #722ed1;"></i>
                                执行详情
                            </h3>
                            <div style="background-color: #f9f9f9; border-radius: 8px; padding: 15px; margin-bottom: 20px;">
                                <div style="display: flex; justify-content: space-between; margin-bottom: 15px;">
                                    <div>
                                        <h4 style="margin: 0 0 5px 0; font-size: 16px;">工作日标准运行计划</h4>
                                        <div style="font-size: 14px; color: #666;">DO控制参数调度 | 计划ID: PLAN-20250515-001</div>
                                    </div>
                                    <div>
                                        <span class="status-tag status-running">运行中</span>
                                    </div>
                                </div>

                                <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 15px; margin-bottom: 15px;">
                                    <div>
                                        <div style="font-size: 13px; color: #666; margin-bottom: 3px;">开始时间</div>
                                        <div style="font-size: 14px; font-weight: 500;">2025-05-15 08:00:00</div>
                                    </div>
                                    <div>
                                        <div style="font-size: 13px; color: #666; margin-bottom: 3px;">预计结束时间</div>
                                        <div style="font-size: 14px; font-weight: 500;">2025-05-15 18:00:00</div>
                                    </div>
                                    <div>
                                        <div style="font-size: 13px; color: #666; margin-bottom: 3px;">已运行时间</div>
                                        <div style="font-size: 14px; font-weight: 500;">05:12:45</div>
                                    </div>
                                    <div>
                                        <div style="font-size: 13px; color: #666; margin-bottom: 3px;">执行进度</div>
                                        <div style="font-size: 14px; font-weight: 500;">65%</div>
                                    </div>
                                </div>

                                <div style="margin-bottom: 15px;">
                                    <div style="font-size: 14px; font-weight: 500; margin-bottom: 8px;">当前执行参数</div>
                                    <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 10px;">
                                        <div>
                                            <div style="font-size: 13px; color: #666; margin-bottom: 3px;">DO目标值</div>
                                            <div style="font-size: 14px;">3.0 mg/L</div>
                                        </div>
                                        <div>
                                            <div style="font-size: 13px; color: #666; margin-bottom: 3px;">比例系数 (P)</div>
                                            <div style="font-size: 14px;">0.5</div>
                                        </div>
                                        <div>
                                            <div style="font-size: 13px; color: #666; margin-bottom: 3px;">积分系数 (I)</div>
                                            <div style="font-size: 14px;">0.2</div>
                                        </div>
                                        <div>
                                            <div style="font-size: 13px; color: #666; margin-bottom: 3px;">微分系数 (D)</div>
                                            <div style="font-size: 14px;">0.1</div>
                                        </div>
                                    </div>
                                </div>

                                <div>
                                    <div style="font-size: 14px; font-weight: 500; margin-bottom: 8px;">执行日志</div>
                                    <div style="max-height: 150px; overflow-y: auto; background-color: #f0f0f0; border-radius: 4px; padding: 10px; font-family: monospace; font-size: 12px;">
                                        <div>[2025-05-15 08:00:00] 计划开始执行</div>
                                        <div>[2025-05-15 08:00:05] 加载DO控制参数: DO=2.5mg/L, P=0.5, I=0.2, D=0.1</div>
                                        <div>[2025-05-15 08:00:10] 参数下发成功</div>
                                        <div>[2025-05-15 12:00:00] 时段切换: 早晨 -> 中午</div>
                                        <div>[2025-05-15 12:00:05] 更新DO控制参数: DO=3.0mg/L, P=0.5, I=0.2, D=0.1</div>
                                        <div>[2025-05-15 12:00:10] 参数下发成功</div>
                                        <div>[2025-05-15 13:15:30] 系统检测: 运行正常</div>
                                    </div>
                                </div>
                            </div>

                            <div class="btn-group">
                                <button class="btn btn-secondary">
                                    <i class="fas fa-file-export"></i> 导出详情
                                </button>
                                <button class="btn btn-primary">
                                    <i class="fas fa-eye"></i> 查看完整日志
                                </button>
                            </div>
                        </div>
                    </div>

                    <!-- 执行历史标签页 -->
                    <div class="tab-content" id="execution-history">
                        <!-- 历史记录查询 -->
                        <div class="form-section">
                            <h3 class="section-title">
                                <i class="fas fa-search" style="color: #1890ff;"></i>
                                历史记录查询
                            </h3>
                            <div class="form-row">
                                <div class="form-group">
                                    <label class="form-label">计划名称</label>
                                    <input type="text" class="form-control" placeholder="输入计划名称">
                                </div>
                                <div class="form-group">
                                    <label class="form-label">计划类型</label>
                                    <select class="form-control">
                                        <option value="all" selected>所有类型</option>
                                        <option value="device">曝气设备调度</option>
                                        <option value="do-param">DO控制参数调度</option>
                                        <option value="energy">能源优化调度</option>
                                    </select>
                                </div>
                            </div>

                            <div class="form-row">
                                <div class="form-group">
                                    <label class="form-label">执行状态</label>
                                    <select class="form-control">
                                        <option value="all" selected>所有状态</option>
                                        <option value="completed">执行完成</option>
                                        <option value="terminated">手动终止</option>
                                        <option value="failed">执行失败</option>
                                    </select>
                                </div>
                                <div class="form-group">
                                    <label class="form-label">执行时间范围</label>
                                    <div style="display: flex; align-items: center; gap: 10px;">
                                        <input type="date" class="form-control" value="2025-05-01">
                                        <span>至</span>
                                        <input type="date" class="form-control" value="2025-05-15">
                                    </div>
                                </div>
                            </div>

                            <div class="btn-group" style="justify-content: flex-start;">
                                <button class="btn btn-primary">
                                    <i class="fas fa-search"></i> 查询
                                </button>
                                <button class="btn btn-secondary">
                                    <i class="fas fa-redo"></i> 重置
                                </button>
                            </div>
                        </div>

                        <!-- 历史记录列表 -->
                        <div class="form-section">
                            <h3 class="section-title">
                                <i class="fas fa-history" style="color: #52c41a;"></i>
                                历史记录列表
                            </h3>
                            <div class="table-container">
                                <table class="data-table">
                                    <thead>
                                        <tr>
                                            <th>计划名称</th>
                                            <th>类型</th>
                                            <th>开始时间</th>
                                            <th>结束时间</th>
                                            <th>执行时长</th>
                                            <th>状态</th>
                                            <th>操作</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <tr>
                                            <td>工作日标准运行计划</td>
                                            <td>DO控制参数调度</td>
                                            <td>2025-05-14 08:00:00</td>
                                            <td>2025-05-14 18:00:00</td>
                                            <td>10小时</td>
                                            <td><span class="status-tag status-completed">执行完成</span></td>
                                            <td>
                                                <button class="btn btn-secondary" style="padding: 4px 8px;" onclick="viewExecutionDetails('EXEC-20250514-001')">
                                                    <i class="fas fa-eye"></i>
                                                </button>
                                                <button class="btn btn-secondary" style="padding: 4px 8px;" onclick="exportExecutionLog('EXEC-20250514-001')">
                                                    <i class="fas fa-download"></i>
                                                </button>
                                            </td>
                                        </tr>
                                        <tr>
                                            <td>峰谷电价响应计划</td>
                                            <td>能源优化调度</td>
                                            <td>2025-05-14 06:00:00</td>
                                            <td>2025-05-15 06:00:00</td>
                                            <td>24小时</td>
                                            <td><span class="status-tag status-completed">执行完成</span></td>
                                            <td>
                                                <button class="btn btn-secondary" style="padding: 4px 8px;" onclick="viewExecutionDetails('EXEC-20250514-002')">
                                                    <i class="fas fa-eye"></i>
                                                </button>
                                                <button class="btn btn-secondary" style="padding: 4px 8px;" onclick="exportExecutionLog('EXEC-20250514-002')">
                                                    <i class="fas fa-download"></i>
                                                </button>
                                            </td>
                                        </tr>
                                        <tr>
                                            <td>设备维护模式</td>
                                            <td>曝气设备调度</td>
                                            <td>2025-05-13 14:00:00</td>
                                            <td>2025-05-14 10:30:00</td>
                                            <td>20小时30分钟</td>
                                            <td><span class="status-tag status-terminated">手动终止</span></td>
                                            <td>
                                                <button class="btn btn-secondary" style="padding: 4px 8px;" onclick="viewExecutionDetails('EXEC-20250513-001')">
                                                    <i class="fas fa-eye"></i>
                                                </button>
                                                <button class="btn btn-secondary" style="padding: 4px 8px;" onclick="exportExecutionLog('EXEC-20250513-001')">
                                                    <i class="fas fa-download"></i>
                                                </button>
                                            </td>
                                        </tr>
                                        <tr>
                                            <td>高负荷应对计划</td>
                                            <td>DO控制参数调度</td>
                                            <td>2025-05-12 10:00:00</td>
                                            <td>2025-05-12 18:00:00</td>
                                            <td>8小时</td>
                                            <td><span class="status-tag status-completed">执行完成</span></td>
                                            <td>
                                                <button class="btn btn-secondary" style="padding: 4px 8px;" onclick="viewExecutionDetails('EXEC-20250512-001')">
                                                    <i class="fas fa-eye"></i>
                                                </button>
                                                <button class="btn btn-secondary" style="padding: 4px 8px;" onclick="exportExecutionLog('EXEC-20250512-001')">
                                                    <i class="fas fa-download"></i>
                                                </button>
                                            </td>
                                        </tr>
                                        <tr>
                                            <td>节能运行计划</td>
                                            <td>能源优化调度</td>
                                            <td>2025-05-11 20:00:00</td>
                                            <td>2025-05-12 06:00:00</td>
                                            <td>10小时</td>
                                            <td><span class="status-tag status-completed">执行完成</span></td>
                                            <td>
                                                <button class="btn btn-secondary" style="padding: 4px 8px;" onclick="viewExecutionDetails('EXEC-20250511-001')">
                                                    <i class="fas fa-eye"></i>
                                                </button>
                                                <button class="btn btn-secondary" style="padding: 4px 8px;" onclick="exportExecutionLog('EXEC-20250511-001')">
                                                    <i class="fas fa-download"></i>
                                                </button>
                                            </td>
                                        </tr>
                                        <tr>
                                            <td>应急处理计划</td>
                                            <td>曝气设备调度</td>
                                            <td>2025-05-10 15:30:00</td>
                                            <td>2025-05-10 18:45:00</td>
                                            <td>3小时15分钟</td>
                                            <td><span class="status-tag status-failed">执行失败</span></td>
                                            <td>
                                                <button class="btn btn-secondary" style="padding: 4px 8px;" onclick="viewExecutionDetails('EXEC-20250510-001')">
                                                    <i class="fas fa-eye"></i>
                                                </button>
                                                <button class="btn btn-secondary" style="padding: 4px 8px;" onclick="exportExecutionLog('EXEC-20250510-001')">
                                                    <i class="fas fa-download"></i>
                                                </button>
                                            </td>
                                        </tr>
                                    </tbody>
                                </table>
                            </div>

                            <div style="display: flex; justify-content: space-between; margin-top: 15px;">
                                <div>
                                    <span style="font-size: 14px; color: #666;">共 24 条记录，当前显示 1-6 条</span>
                                </div>
                                <div style="display: flex; gap: 10px;">
                                    <button class="btn btn-secondary" disabled>
                                        <i class="fas fa-chevron-left"></i> 上一页
                                    </button>
                                    <button class="btn btn-secondary">
                                        下一页 <i class="fas fa-chevron-right"></i>
                                    </button>
                                </div>
                            </div>
                        </div>

                        <!-- 执行详情 -->
                        <div class="form-section">
                            <h3 class="section-title">
                                <i class="fas fa-file-alt" style="color: #722ed1;"></i>
                                执行详情
                            </h3>
                            <div style="background-color: #f9f9f9; border-radius: 8px; padding: 15px; margin-bottom: 20px;">
                                <div style="display: flex; justify-content: space-between; margin-bottom: 15px;">
                                    <div>
                                        <h4 style="margin: 0 0 5px 0; font-size: 16px;">工作日标准运行计划</h4>
                                        <div style="font-size: 14px; color: #666;">DO控制参数调度 | 执行ID: EXEC-20250514-001</div>
                                    </div>
                                    <div>
                                        <span class="status-tag status-completed">执行完成</span>
                                    </div>
                                </div>

                                <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 15px; margin-bottom: 15px;">
                                    <div>
                                        <div style="font-size: 13px; color: #666; margin-bottom: 3px;">开始时间</div>
                                        <div style="font-size: 14px; font-weight: 500;">2025-05-14 08:00:00</div>
                                    </div>
                                    <div>
                                        <div style="font-size: 13px; color: #666; margin-bottom: 3px;">结束时间</div>
                                        <div style="font-size: 14px; font-weight: 500;">2025-05-14 18:00:00</div>
                                    </div>
                                    <div>
                                        <div style="font-size: 13px; color: #666; margin-bottom: 3px;">执行时长</div>
                                        <div style="font-size: 14px; font-weight: 500;">10小时</div>
                                    </div>
                                    <div>
                                        <div style="font-size: 13px; color: #666; margin-bottom: 3px;">执行结果</div>
                                        <div style="font-size: 14px; font-weight: 500; color: #52c41a;">成功</div>
                                    </div>
                                </div>

                                <div style="margin-bottom: 15px;">
                                    <div style="font-size: 14px; font-weight: 500; margin-bottom: 8px;">执行参数</div>
                                    <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 10px;">
                                        <div>
                                            <div style="font-size: 13px; color: #666; margin-bottom: 3px;">早晨DO目标值</div>
                                            <div style="font-size: 14px;">2.5 mg/L</div>
                                        </div>
                                        <div>
                                            <div style="font-size: 13px; color: #666; margin-bottom: 3px;">中午DO目标值</div>
                                            <div style="font-size: 14px;">3.0 mg/L</div>
                                        </div>
                                        <div>
                                            <div style="font-size: 13px; color: #666; margin-bottom: 3px;">晚上DO目标值</div>
                                            <div style="font-size: 14px;">1.8 mg/L</div>
                                        </div>
                                        <div>
                                            <div style="font-size: 13px; color: #666; margin-bottom: 3px;">PID参数</div>
                                            <div style="font-size: 14px;">P=0.5, I=0.2, D=0.1</div>
                                        </div>
                                    </div>
                                </div>

                                <div style="margin-bottom: 15px;">
                                    <div style="font-size: 14px; font-weight: 500; margin-bottom: 8px;">执行统计</div>
                                    <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 10px;">
                                        <div>
                                            <div style="font-size: 13px; color: #666; margin-bottom: 3px;">平均DO值</div>
                                            <div style="font-size: 14px;">2.7 mg/L</div>
                                        </div>
                                        <div>
                                            <div style="font-size: 13px; color: #666; margin-bottom: 3px;">能耗</div>
                                            <div style="font-size: 14px;">120 kWh</div>
                                        </div>
                                        <div>
                                            <div style="font-size: 13px; color: #666; margin-bottom: 3px;">处理水量</div>
                                            <div style="font-size: 14px;">5000 m³</div>
                                        </div>
                                        <div>
                                            <div style="font-size: 13px; color: #666; margin-bottom: 3px;">单位能耗</div>
                                            <div style="font-size: 14px;">0.024 kWh/m³</div>
                                        </div>
                                    </div>
                                </div>

                                <div>
                                    <div style="font-size: 14px; font-weight: 500; margin-bottom: 8px;">执行日志摘要</div>
                                    <div style="max-height: 150px; overflow-y: auto; background-color: #f0f0f0; border-radius: 4px; padding: 10px; font-family: monospace; font-size: 12px;">
                                        <div>[2025-05-14 08:00:00] 计划开始执行</div>
                                        <div>[2025-05-14 08:00:05] 加载DO控制参数: DO=2.5mg/L, P=0.5, I=0.2, D=0.1</div>
                                        <div>[2025-05-14 08:00:10] 参数下发成功</div>
                                        <div>[2025-05-14 12:00:00] 时段切换: 早晨 -> 中午</div>
                                        <div>[2025-05-14 12:00:05] 更新DO控制参数: DO=3.0mg/L, P=0.5, I=0.2, D=0.1</div>
                                        <div>[2025-05-14 12:00:10] 参数下发成功</div>
                                        <div>[2025-05-14 18:00:00] 时段切换: 中午 -> 晚上</div>
                                        <div>[2025-05-14 18:00:05] 更新DO控制参数: DO=1.8mg/L, P=0.5, I=0.2, D=0.1</div>
                                        <div>[2025-05-14 18:00:10] 参数下发成功</div>
                                        <div>[2025-05-14 18:00:15] 计划执行完成</div>
                                    </div>
                                </div>
                            </div>

                            <div class="chart-container">
                                <div style="text-align: center; padding: 20px;">
                                    <i class="fas fa-chart-line" style="font-size: 48px; color: #d9d9d9;"></i>
                                    <p>执行过程DO值和鼓风机运行趋势图将在此显示</p>
                                </div>
                            </div>

                            <div class="btn-group">
                                <button class="btn btn-secondary">
                                    <i class="fas fa-file-export"></i> 导出详情
                                </button>
                                <button class="btn btn-primary">
                                    <i class="fas fa-eye"></i> 查看完整日志
                                </button>
                            </div>
                        </div>
                    </div>

                    <!-- 手动干预标签页 -->
                    <div class="tab-content" id="manual-intervention">
                        <!-- 当前执行计划 -->
                        <div class="form-section">
                            <h3 class="section-title">
                                <i class="fas fa-play-circle" style="color: #1890ff;"></i>
                                当前执行计划
                            </h3>
                            <div class="table-container">
                                <table class="data-table">
                                    <thead>
                                        <tr>
                                            <th>计划名称</th>
                                            <th>类型</th>
                                            <th>开始时间</th>
                                            <th>预计结束时间</th>
                                            <th>执行进度</th>
                                            <th>状态</th>
                                            <th>操作</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <tr>
                                            <td>工作日标准运行计划</td>
                                            <td>DO控制参数调度</td>
                                            <td>2025-05-15 08:00:00</td>
                                            <td>2025-05-15 18:00:00</td>
                                            <td>
                                                <div style="width: 100%; background-color: #f0f0f0; border-radius: 4px;">
                                                    <div style="width: 65%; height: 8px; background-color: #1890ff; border-radius: 4px;"></div>
                                                </div>
                                                <div style="text-align: right; font-size: 12px; color: #666;">65%</div>
                                            </td>
                                            <td><span class="status-tag status-running">运行中</span></td>
                                            <td>
                                                <button class="btn btn-primary" style="padding: 4px 8px;" onclick="selectPlanForIntervention('PLAN-20250515-001')">
                                                    <i class="fas fa-hand-paper"></i> 干预
                                                </button>
                                            </td>
                                        </tr>
                                        <tr>
                                            <td>峰谷电价响应计划</td>
                                            <td>能源优化调度</td>
                                            <td>2025-05-15 06:00:00</td>
                                            <td>2025-05-16 06:00:00</td>
                                            <td>
                                                <div style="width: 100%; background-color: #f0f0f0; border-radius: 4px;">
                                                    <div style="width: 40%; height: 8px; background-color: #1890ff; border-radius: 4px;"></div>
                                                </div>
                                                <div style="text-align: right; font-size: 12px; color: #666;">40%</div>
                                            </td>
                                            <td><span class="status-tag status-running">运行中</span></td>
                                            <td>
                                                <button class="btn btn-primary" style="padding: 4px 8px;" onclick="selectPlanForIntervention('PLAN-20250515-002')">
                                                    <i class="fas fa-hand-paper"></i> 干预
                                                </button>
                                            </td>
                                        </tr>
                                        <tr>
                                            <td>鼓风机轮换计划</td>
                                            <td>曝气设备调度</td>
                                            <td>2025-05-15 08:00:00</td>
                                            <td>2025-05-22 08:00:00</td>
                                            <td>
                                                <div style="width: 100%; background-color: #f0f0f0; border-radius: 4px;">
                                                    <div style="width: 10%; height: 8px; background-color: #1890ff; border-radius: 4px;"></div>
                                                </div>
                                                <div style="text-align: right; font-size: 12px; color: #666;">10%</div>
                                            </td>
                                            <td><span class="status-tag status-running">运行中</span></td>
                                            <td>
                                                <button class="btn btn-primary" style="padding: 4px 8px;" onclick="selectPlanForIntervention('PLAN-20250515-003')">
                                                    <i class="fas fa-hand-paper"></i> 干预
                                                </button>
                                            </td>
                                        </tr>
                                        <tr>
                                            <td>设备维护模式</td>
                                            <td>曝气设备调度</td>
                                            <td>2025-05-14 14:00:00</td>
                                            <td>2025-05-16 14:00:00</td>
                                            <td>
                                                <div style="width: 100%; background-color: #f0f0f0; border-radius: 4px;">
                                                    <div style="width: 50%; height: 8px; background-color: #faad14; border-radius: 4px;"></div>
                                                </div>
                                                <div style="text-align: right; font-size: 12px; color: #666;">50%</div>
                                            </td>
                                            <td><span class="status-tag status-paused">已暂停</span></td>
                                            <td>
                                                <button class="btn btn-primary" style="padding: 4px 8px;" onclick="selectPlanForIntervention('PLAN-20250514-001')">
                                                    <i class="fas fa-hand-paper"></i> 干预
                                                </button>
                                            </td>
                                        </tr>
                                    </tbody>
                                </table>
                            </div>
                        </div>

                        <!-- 干预操作 -->
                        <div class="form-section">
                            <h3 class="section-title">
                                <i class="fas fa-sliders-h" style="color: #52c41a;"></i>
                                干预操作
                            </h3>
                            <div style="background-color: #f9f9f9; border-radius: 8px; padding: 15px; margin-bottom: 20px;">
                                <div style="display: flex; justify-content: space-between; margin-bottom: 15px;">
                                    <div>
                                        <h4 style="margin: 0 0 5px 0; font-size: 16px;">工作日标准运行计划</h4>
                                        <div style="font-size: 14px; color: #666;">DO控制参数调度 | 计划ID: PLAN-20250515-001</div>
                                    </div>
                                    <div>
                                        <span class="status-tag status-running">运行中</span>
                                    </div>
                                </div>

                                <div class="form-row">
                                    <div class="form-group">
                                        <label class="form-label">干预类型</label>
                                        <select class="form-control" id="intervention-type" onchange="changeInterventionType()">
                                            <option value="pause">暂停计划</option>
                                            <option value="resume">恢复计划</option>
                                            <option value="stop">终止计划</option>
                                            <option value="modify" selected>修改参数</option>
                                            <option value="override">临时覆盖</option>
                                        </select>
                                    </div>
                                    <div class="form-group">
                                        <label class="form-label">干预原因</label>
                                        <select class="form-control">
                                            <option>水质异常</option>
                                            <option>设备维护</option>
                                            <option>能耗优化</option>
                                            <option>工艺调整</option>
                                            <option selected>参数优化</option>
                                            <option>其他原因</option>
                                        </select>
                                    </div>
                                </div>

                                <!-- 修改参数干预 -->
                                <div id="modify-params-intervention">
                                    <div class="form-row">
                                        <div class="form-group">
                                            <label class="form-label">DO目标值 (mg/L)</label>
                                            <input type="number" class="form-control" value="3.0" min="0" max="10" step="0.1">
                                        </div>
                                        <div class="form-group">
                                            <label class="form-label">比例系数 (P)</label>
                                            <input type="number" class="form-control" value="0.5" step="0.1">
                                        </div>
                                    </div>

                                    <div class="form-row">
                                        <div class="form-group">
                                            <label class="form-label">积分系数 (I)</label>
                                            <input type="number" class="form-control" value="0.2" step="0.1">
                                        </div>
                                        <div class="form-group">
                                            <label class="form-label">微分系数 (D)</label>
                                            <input type="number" class="form-control" value="0.1" step="0.1">
                                        </div>
                                    </div>

                                    <div class="form-row">
                                        <div class="form-group">
                                            <label class="form-label">修改持续时间</label>
                                            <select class="form-control">
                                                <option>仅当前时段</option>
                                                <option selected>本次执行期间</option>
                                                <option>永久修改</option>
                                            </select>
                                        </div>
                                    </div>
                                </div>

                                <!-- 临时覆盖干预 -->
                                <div id="override-intervention" style="display: none;">
                                    <div class="form-row">
                                        <div class="form-group">
                                            <label class="form-label">覆盖计划</label>
                                            <select class="form-control">
                                                <option>-- 选择覆盖计划 --</option>
                                                <option>高负荷应对计划</option>
                                                <option>节能运行计划</option>
                                                <option selected>应急处理计划</option>
                                                <option>设备维护模式</option>
                                            </select>
                                        </div>
                                        <div class="form-group">
                                            <label class="form-label">覆盖持续时间</label>
                                            <div style="display: flex; align-items: center; gap: 10px;">
                                                <input type="number" class="form-control" value="2" min="1" style="width: 80px;">
                                                <select class="form-control">
                                                    <option>分钟</option>
                                                    <option selected>小时</option>
                                                    <option>天</option>
                                                </select>
                                            </div>
                                        </div>
                                    </div>

                                    <div class="form-row">
                                        <div class="form-group">
                                            <label class="form-label">覆盖结束后</label>
                                            <select class="form-control">
                                                <option selected>恢复原计划</option>
                                                <option>暂停原计划</option>
                                                <option>终止原计划</option>
                                            </select>
                                        </div>
                                    </div>
                                </div>

                                <div class="form-row">
                                    <div class="form-group">
                                        <label class="form-label">备注说明</label>
                                        <textarea class="form-control" rows="3" placeholder="输入干预操作的详细说明...">优化DO控制参数，提高控制精度</textarea>
                                    </div>
                                </div>
                            </div>

                            <div class="btn-group">
                                <button class="btn btn-secondary">取消</button>
                                <button class="btn btn-primary" onclick="applyIntervention()">
                                    <i class="fas fa-check"></i> 应用干预
                                </button>
                            </div>
                        </div>

                        <!-- 干预历史 -->
                        <div class="form-section">
                            <h3 class="section-title">
                                <i class="fas fa-history" style="color: #722ed1;"></i>
                                干预历史
                            </h3>
                            <div class="table-container">
                                <table class="data-table">
                                    <thead>
                                        <tr>
                                            <th>干预时间</th>
                                            <th>计划名称</th>
                                            <th>干预类型</th>
                                            <th>操作人员</th>
                                            <th>干预原因</th>
                                            <th>状态</th>
                                            <th>操作</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <tr>
                                            <td>2025-05-15 10:30:15</td>
                                            <td>工作日标准运行计划</td>
                                            <td>修改参数</td>
                                            <td>管理员</td>
                                            <td>参数优化</td>
                                            <td><span class="status-tag status-running">生效中</span></td>
                                            <td>
                                                <button class="btn btn-secondary" style="padding: 4px 8px;">
                                                    <i class="fas fa-eye"></i>
                                                </button>
                                                <button class="btn btn-danger" style="padding: 4px 8px;">
                                                    <i class="fas fa-undo"></i>
                                                </button>
                                            </td>
                                        </tr>
                                        <tr>
                                            <td>2025-05-14 16:45:30</td>
                                            <td>设备维护模式</td>
                                            <td>暂停计划</td>
                                            <td>工程师</td>
                                            <td>设备维护</td>
                                            <td><span class="status-tag status-running">生效中</span></td>
                                            <td>
                                                <button class="btn btn-secondary" style="padding: 4px 8px;">
                                                    <i class="fas fa-eye"></i>
                                                </button>
                                                <button class="btn btn-danger" style="padding: 4px 8px;">
                                                    <i class="fas fa-undo"></i>
                                                </button>
                                            </td>
                                        </tr>
                                        <tr>
                                            <td>2025-05-13 09:20:45</td>
                                            <td>峰谷电价响应计划</td>
                                            <td>临时覆盖</td>
                                            <td>管理员</td>
                                            <td>能耗优化</td>
                                            <td><span class="status-tag status-completed">已结束</span></td>
                                            <td>
                                                <button class="btn btn-secondary" style="padding: 4px 8px;">
                                                    <i class="fas fa-eye"></i>
                                                </button>
                                            </td>
                                        </tr>
                                        <tr>
                                            <td>2025-05-12 14:10:20</td>
                                            <td>高负荷应对计划</td>
                                            <td>修改参数</td>
                                            <td>工程师</td>
                                            <td>水质异常</td>
                                            <td><span class="status-tag status-completed">已结束</span></td>
                                            <td>
                                                <button class="btn btn-secondary" style="padding: 4px 8px;">
                                                    <i class="fas fa-eye"></i>
                                                </button>
                                            </td>
                                        </tr>
                                        <tr>
                                            <td>2025-05-10 18:30:00</td>
                                            <td>应急处理计划</td>
                                            <td>终止计划</td>
                                            <td>管理员</td>
                                            <td>工艺调整</td>
                                            <td><span class="status-tag status-completed">已结束</span></td>
                                            <td>
                                                <button class="btn btn-secondary" style="padding: 4px 8px;">
                                                    <i class="fas fa-eye"></i>
                                                </button>
                                            </td>
                                        </tr>
                                    </tbody>
                                </table>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="js/common.js"></script>
    <script>
        // 初始化公共元素
        document.addEventListener('DOMContentLoaded', function() {
            // 标签页切换
            const tabItems = document.querySelectorAll('.tab-item');
            const tabContents = document.querySelectorAll('.tab-content');

            tabItems.forEach(item => {
                item.addEventListener('click', function() {
                    const tabId = this.getAttribute('data-tab');

                    // 移除所有标签和内容的激活状态
                    tabItems.forEach(tab => tab.classList.remove('active'));
                    tabContents.forEach(content => content.classList.remove('active'));

                    // 激活当前标签和内容
                    this.classList.add('active');
                    document.getElementById(tabId).classList.add('active');
                });
            });

            // 初始化实时数据更新
            initRealTimeUpdates();
        });

        // 初始化实时数据更新
        function initRealTimeUpdates() {
            // 在实际应用中，这里会使用WebSocket或轮询来获取实时数据
            // 这里使用定时器模拟实时数据更新
            setInterval(function() {
                // 更新进度条
                updateProgressBars();

                // 更新运行时间
                updateRunningTime();
            }, 5000);
        }

        // 更新进度条
        function updateProgressBars() {
            // 在实际应用中，这里会从后端获取最新的进度数据
            // 这里使用随机数模拟进度变化
            const progressBars = document.querySelectorAll('.tab-content.active .progress-bar');
            progressBars.forEach(bar => {
                const currentWidth = parseInt(bar.style.width) || 0;
                // 只有运行中的计划才会更新进度
                if (currentWidth < 100 && bar.closest('tr')?.querySelector('.status-running')) {
                    const newWidth = Math.min(currentWidth + Math.random() * 2, 100);
                    bar.style.width = newWidth + '%';
                    const progressText = bar.parentElement.nextElementSibling;
                    if (progressText) {
                        progressText.textContent = Math.round(newWidth) + '%';
                    }
                }
            });
        }

        // 更新运行时间
        function updateRunningTime() {
            // 在实际应用中，这里会从后端获取最新的运行时间
            // 这里使用简单的时间增加模拟
            const runningTimeElement = document.querySelector('#execution-status .execution-details .running-time');
            if (runningTimeElement) {
                const timeParts = runningTimeElement.textContent.split(':');
                let hours = parseInt(timeParts[0]);
                let minutes = parseInt(timeParts[1]);
                let seconds = parseInt(timeParts[2]);

                seconds += 5;
                if (seconds >= 60) {
                    seconds = 0;
                    minutes += 1;
                }
                if (minutes >= 60) {
                    minutes = 0;
                    hours += 1;
                }

                runningTimeElement.textContent =
                    String(hours).padStart(2, '0') + ':' +
                    String(minutes).padStart(2, '0') + ':' +
                    String(seconds).padStart(2, '0');
            }
        }

        // 刷新数据
        function refreshData() {
            showNotification('正在刷新数据...', 'info');

            // 模拟刷新延迟
            setTimeout(function() {
                // 更新所有进度条
                updateAllProgressBars();

                // 更新所有状态信息
                updateAllStatusInfo();

                showNotification('数据已刷新', 'success');
            }, 1000);
        }

        // 更新所有进度条
        function updateAllProgressBars() {
            const progressBars = document.querySelectorAll('.progress-bar');
            progressBars.forEach(bar => {
                const currentWidth = parseInt(bar.style.width) || 0;
                // 只有运行中的计划才会更新进度
                if (currentWidth < 100 && bar.closest('tr')?.querySelector('.status-running')) {
                    const newWidth = Math.min(currentWidth + Math.random() * 5, 100);
                    bar.style.width = newWidth + '%';
                    const progressText = bar.parentElement.nextElementSibling;
                    if (progressText) {
                        progressText.textContent = Math.round(newWidth) + '%';
                    }
                }
            });
        }

        // 更新所有状态信息
        function updateAllStatusInfo() {
            // 在实际应用中，这里会从后端获取最新的状态信息
            // 这里仅模拟状态更新
            console.log('更新所有状态信息');
        }

        // 查看执行详情
        function viewExecutionDetails(executionId) {
            showNotification('正在加载执行详情...', 'info');

            // 模拟加载延迟
            setTimeout(function() {
                // 在实际应用中，这里会从后端获取执行详情数据
                // 这里仅模拟显示详情
                document.getElementById('execution-history').scrollIntoView({ behavior: 'smooth', block: 'start' });

                // 滚动到执行详情部分
                const detailsSection = document.querySelector('#execution-history .form-section:last-child');
                if (detailsSection) {
                    detailsSection.scrollIntoView({ behavior: 'smooth', block: 'start' });
                }

                showNotification('执行详情已加载', 'success');
            }, 800);
        }

        // 导出执行日志
        function exportExecutionLog(executionId) {
            showNotification('正在导出执行日志...', 'info');

            // 模拟导出延迟
            setTimeout(function() {
                // 在实际应用中，这里会调用后端API导出日志
                // 这里仅模拟导出完成
                showNotification('执行日志已导出', 'success');
            }, 1200);
        }

        // 选择计划进行干预
        function selectPlanForIntervention(planId) {
            showNotification('正在加载计划信息...', 'info');

            // 模拟加载延迟
            setTimeout(function() {
                // 在实际应用中，这里会从后端获取计划详情数据
                // 这里仅模拟显示干预界面

                // 切换到手动干预标签页
                const tabItems = document.querySelectorAll('.tab-item');
                const tabContents = document.querySelectorAll('.tab-content');

                tabItems.forEach(tab => tab.classList.remove('active'));
                tabContents.forEach(content => content.classList.remove('active'));

                const manualTab = document.querySelector('.tab-item[data-tab="manual-intervention"]');
                if (manualTab) {
                    manualTab.classList.add('active');
                }

                const manualContent = document.getElementById('manual-intervention');
                if (manualContent) {
                    manualContent.classList.add('active');
                }

                // 滚动到干预操作部分
                const interventionSection = document.querySelector('#manual-intervention .form-section:nth-child(2)');
                if (interventionSection) {
                    interventionSection.scrollIntoView({ behavior: 'smooth', block: 'start' });
                }

                showNotification('计划信息已加载', 'success');
            }, 800);
        }

        // 切换干预类型
        function changeInterventionType() {
            const interventionType = document.getElementById('intervention-type').value;

            // 隐藏所有干预类型的表单
            document.getElementById('modify-params-intervention').style.display = 'none';
            document.getElementById('override-intervention').style.display = 'none';

            // 显示选中的干预类型表单
            if (interventionType === 'modify') {
                document.getElementById('modify-params-intervention').style.display = 'block';
            } else if (interventionType === 'override') {
                document.getElementById('override-intervention').style.display = 'block';
            }
        }

        // 应用干预
        function applyIntervention() {
            showNotification('正在应用干预操作...', 'info');

            // 模拟处理延迟
            setTimeout(function() {
                // 在实际应用中，这里会调用后端API应用干预操作
                // 这里仅模拟操作完成
                showNotification('干预操作已成功应用', 'success');

                // 滚动到干预历史部分
                const historySection = document.querySelector('#manual-intervention .form-section:last-child');
                if (historySection) {
                    historySection.scrollIntoView({ behavior: 'smooth', block: 'start' });
                }
            }, 1500);
        }

        // 显示通知
        function showNotification(message, type) {
            // 假设common.js中已经实现了这个函数
            if (window.showNotification) {
                window.showNotification(message, type);
            } else {
                // 简单的通知实现
                const notification = document.createElement('div');
                notification.className = `notification ${type}`;
                notification.innerHTML = `
                    <div class="notification-content">
                        <i class="fas fa-${type === 'success' ? 'check-circle' : type === 'error' ? 'times-circle' : 'info-circle'}"></i>
                        <span>${message}</span>
                    </div>
                `;
                document.body.appendChild(notification);

                // 显示通知
                setTimeout(() => notification.classList.add('show'), 10);

                // 3秒后隐藏通知
                setTimeout(() => {
                    notification.classList.remove('show');
                    setTimeout(() => document.body.removeChild(notification), 300);
                }, 3000);
            }
        }
    </script>
</body>
</html>
