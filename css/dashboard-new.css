/* 新的仪表盘样式 - 参考提供的设计图 */

/* 侧边栏样式调整 */
.sidebar {
  background-color: #fff;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  color: #333;
  width: 200px;
}

.sidebar-menu {
  padding: 0;
}

/* 菜单分类 */
.menu-category {
  margin-bottom: 10px;
}

.menu-category-title {
  padding: 10px 20px;
  font-size: 12px;
  color: #999;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.menu-item {
  padding: 12px 20px;
  color: #666;
  border-left: none;
  border-radius: 0;
  margin-bottom: 2px;
  position: relative;
  display: flex;
  align-items: center;
  cursor: pointer;
  transition: all 0.2s;
}

.menu-item:hover {
  background-color: rgba(24, 144, 255, 0.05);
  color: #1890ff;
}

.menu-item.active {
  background-color: rgba(24, 144, 255, 0.1);
  color: #1890ff;
  font-weight: 500;
}

.menu-item.active::before {
  content: '';
  position: absolute;
  left: 0;
  top: 0;
  bottom: 0;
  width: 3px;
  background-color: #1890ff;
}

.menu-item i {
  font-size: 16px;
  width: 20px;
  text-align: center;
  margin-right: 10px;
}

/* 顶部导航样式 */
.topbar {
  background-color: #fff;
  height: 60px;
  box-shadow: 0 1px 4px rgba(0, 0, 0, 0.1);
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 0 20px;
}

.topbar-left {
  display: flex;
  align-items: center;
}

.topbar-right {
  display: flex;
  align-items: center;
  gap: 15px;
}

.topbar-logo {
  height: 36px;
  margin-right: 12px;
}

.topbar-title {
  font-size: 18px;
  font-weight: 600;
  color: #333;
}

.fullscreen-toggle {
  width: 32px;
  height: 32px;
  border-radius: 4px;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  color: #666;
  transition: all 0.2s;
}

.fullscreen-toggle:hover {
  background-color: rgba(0, 0, 0, 0.05);
  color: #1890ff;
}

.user-avatar {
  width: 32px;
  height: 32px;
  border-radius: 50%;
  background-color: #1890ff;
  color: white;
  display: flex;
  align-items: center;
  justify-content: center;
  font-weight: 500;
}

/* 内容区域样式 */
.content {
  background-color: #f5f7fa;
  padding: 24px;
}

/* 标签页样式 */
.tabs-container {
  margin-bottom: 24px;
  border-bottom: 1px solid #e8e8e8;
  display: flex;
}

.tab-item {
  padding: 12px 24px;
  cursor: pointer;
  position: relative;
  color: #666;
  font-weight: 500;
}

.tab-item.active {
  color: #1890ff;
}

.tab-item.active::after {
  content: '';
  position: absolute;
  left: 0;
  right: 0;
  bottom: -1px;
  height: 2px;
  background-color: #1890ff;
}

/* 数据卡片样式 */
.data-card {
  background-color: #fff;
  border-radius: 8px;
  padding: 20px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.08);
  transition: all 0.3s;
  height: 100%;
}

.data-card:hover {
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.12);
  transform: translateY(-3px);
}

.data-card-header {
  font-size: 14px;
  color: #666;
  margin-bottom: 8px;
}

.data-card-value {
  font-size: 28px;
  font-weight: 600;
  margin-bottom: 8px;
  color: #333;
}

.data-card-unit {
  font-size: 14px;
  color: #999;
}

/* 数据卡片颜色变体 */
.data-card.blue {
  background-color: #e6f7ff;
}

.data-card.blue .data-card-value {
  color: #1890ff;
}

.data-card.green {
  background-color: #f6ffed;
}

.data-card.green .data-card-value {
  color: #52c41a;
}

.data-card.yellow {
  background-color: #fffbe6;
}

.data-card.yellow .data-card-value {
  color: #faad14;
}

.data-card.red {
  background-color: #fff1f0;
}

.data-card.red .data-card-value {
  color: #f5222d;
}

.data-card.orange {
  background-color: #fff7e6;
}

.data-card.orange .data-card-value {
  color: #fa8c16;
}

/* 图表容器样式 */
.chart-container {
  background-color: #fff;
  border-radius: 8px;
  padding: 20px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.08);
  margin-bottom: 24px;
}

.chart-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;
}

.chart-title {
  font-size: 16px;
  font-weight: 600;
  color: #333;
}

/* 日期选择器样式 */
.date-range {
  display: flex;
  align-items: center;
  gap: 8px;
}

.date-input {
  padding: 8px 12px;
  border: 1px solid #d9d9d9;
  border-radius: 4px;
  font-size: 14px;
}

/* 表格样式优化 */
.table {
  width: 100%;
  border-collapse: separate;
  border-spacing: 0;
  border-radius: 8px;
  overflow: hidden;
}

.table th {
  background-color: #fafafa;
  font-weight: 600;
  color: #333;
  padding: 14px 16px;
  border-bottom: 1px solid #f0f0f0;
}

.table td {
  padding: 14px 16px;
  border-bottom: 1px solid #f0f0f0;
}

.table tr:hover {
  background-color: #f5f7fa;
}

/* 按钮样式优化 */
.btn {
  border-radius: 4px;
  padding: 8px 16px;
  font-weight: 500;
  transition: all 0.2s;
}

.btn-primary {
  background-color: #1890ff;
  color: white;
  border: none;
}

.btn-primary:hover {
  background-color: #40a9ff;
  box-shadow: 0 2px 8px rgba(24, 144, 255, 0.3);
}
