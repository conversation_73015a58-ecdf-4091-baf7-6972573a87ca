<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>精准曝气控制系统 (GPAC) - 调度管理</title>
    <link rel="stylesheet" href="css/all.min.css">
    <link rel="stylesheet" href="css/style.css">
    <style>
        /* 确保内容不被侧边栏遮挡 */
        .content {
            margin-left: var(--sidebar-width) !important;
            width: calc(100% - var(--sidebar-width)) !important;
            padding: 20px !important;
            box-sizing: border-box !important;
        }

        /* 主容器样式 */
        .scheduling-container {
            padding: 24px;
        }

        /* 页面标题和操作区 */
        .page-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 24px;
            flex-wrap: wrap;
            gap: 16px;
        }

        .page-title {
            font-size: 20px;
            font-weight: 600;
            display: flex;
            align-items: center;
            gap: 10px;
        }

        .header-actions {
            display: flex;
            gap: 12px;
        }

        /* 标签页容器 */
        .tab-container {
            margin-bottom: 24px;
        }

        .tabs {
            display: flex;
            border-bottom: 1px solid var(--border-color);
            margin-bottom: 16px;
        }

        .tab {
            padding: 12px 24px;
            cursor: pointer;
            border-bottom: 2px solid transparent;
            transition: all 0.3s;
            font-weight: 500;
        }

        .tab.active {
            border-bottom-color: var(--primary-color);
            color: var(--primary-color);
        }

        .tab-content {
            display: none;
        }

        .tab-content.active {
            display: block;
        }

        /* 设备控制面板 */
        .control-panel {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 20px;
            margin-bottom: 24px;
        }

        .control-card {
            background: white;
            border-radius: 12px;
            padding: 20px;
            box-shadow: 0 4px 12px rgba(0,0,0,0.08);
            transition: transform 0.2s, box-shadow 0.2s;
        }

        .control-card:hover {
            transform: translateY(-3px);
            box-shadow: 0 6px 16px rgba(0,0,0,0.12);
        }

        .card-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 16px;
        }

        .card-title {
            font-size: 16px;
            font-weight: 600;
            display: flex;
            align-items: center;
            gap: 8px;
        }

        /* 设备状态样式 */
        .device-status {
            display: flex;
            align-items: center;
            margin-bottom: 15px;
        }

        .status-dot {
            width: 12px;
            height: 12px;
            border-radius: 50%;
            margin-right: 8px;
            position: relative;
        }

        .status-dot.status-running::after {
            content: '';
            position: absolute;
            top: -4px;
            left: -4px;
            right: -4px;
            bottom: -4px;
            border-radius: 50%;
            background-color: rgba(82, 196, 26, 0.2);
            animation: pulse 1.5s infinite;
        }

        .status-running {
            background-color: var(--secondary-color);
        }

        .status-stopped {
            background-color: var(--danger-color);
        }

        .status-warning {
            background-color: var(--warning-color);
        }

        .status-idle {
            background-color: #d9d9d9;
        }

        /* 设备信息行 */
        .info-row {
            display: flex;
            justify-content: space-between;
            margin-bottom: 12px;
            color: var(--text-light);
        }

        .info-value {
            font-weight: 500;
            color: var(--text-color);
        }

        /* 控制按钮 */
        .control-buttons {
            display: flex;
            gap: 10px;
            margin-top: 15px;
        }

        /* 滑块容器 */
        .slider-container {
            margin: 15px 0;
        }

        .slider-header {
            display: flex;
            justify-content: space-between;
            margin-bottom: 8px;
        }

        .slider-value {
            font-weight: 500;
        }

        .slider {
            width: 100%;
            height: 6px;
            -webkit-appearance: none;
            appearance: none;
            background: #e1e1e1;
            outline: none;
            border-radius: 3px;
            transition: all 0.3s;
        }

        .slider::-webkit-slider-thumb {
            -webkit-appearance: none;
            appearance: none;
            width: 18px;
            height: 18px;
            border-radius: 50%;
            background: var(--primary-color);
            cursor: pointer;
            box-shadow: 0 2px 5px rgba(0,0,0,0.2);
        }

        .slider::-moz-range-thumb {
            width: 18px;
            height: 18px;
            border-radius: 50%;
            background: var(--primary-color);
            cursor: pointer;
            box-shadow: 0 2px 5px rgba(0,0,0,0.2);
        }

        /* 设备列表表格 */
        .device-table-container {
            background: white;
            border-radius: 12px;
            box-shadow: 0 4px 12px rgba(0,0,0,0.08);
            padding: 20px;
            margin-bottom: 24px;
            overflow-x: auto;
        }

        .device-table {
            width: 100%;
            border-collapse: collapse;
        }

        .device-table th {
            text-align: left;
            padding: 12px 16px;
            background-color: #fafafa;
            border-bottom: 1px solid var(--border-color);
            font-weight: 500;
        }

        .device-table td {
            padding: 12px 16px;
            border-bottom: 1px solid var(--border-color);
        }

        .device-table tr:hover {
            background-color: #f5f5f5;
        }

        .device-table .checkbox-cell {
            width: 40px;
            text-align: center;
        }

        /* 调度计划卡片 */
        .schedule-card {
            background: white;
            border-radius: 12px;
            padding: 20px;
            box-shadow: 0 4px 12px rgba(0,0,0,0.08);
            margin-bottom: 20px;
        }

        .schedule-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 16px;
        }

        .schedule-title {
            font-size: 16px;
            font-weight: 600;
        }

        .schedule-status {
            padding: 4px 12px;
            border-radius: 16px;
            font-size: 12px;
            font-weight: 500;
        }

        .status-active {
            background-color: #e6f7ff;
            color: var(--primary-color);
            border: 1px solid #91d5ff;
        }

        .status-inactive {
            background-color: #f5f5f5;
            color: #999;
            border: 1px solid #d9d9d9;
        }

        .schedule-time {
            display: flex;
            align-items: center;
            gap: 8px;
            margin-bottom: 12px;
            color: var(--text-light);
        }

        .schedule-devices {
            margin-bottom: 12px;
        }

        .schedule-actions {
            display: flex;
            justify-content: flex-end;
            gap: 10px;
            margin-top: 16px;
        }

        /* 历史记录时间线 */
        .timeline {
            position: relative;
            padding-left: 32px;
        }

        .timeline::before {
            content: '';
            position: absolute;
            left: 8px;
            top: 0;
            bottom: 0;
            width: 2px;
            background-color: var(--border-color);
        }

        .timeline-item {
            position: relative;
            margin-bottom: 24px;
        }

        .timeline-item:last-child {
            margin-bottom: 0;
        }

        .timeline-dot {
            position: absolute;
            left: -32px;
            top: 0;
            width: 16px;
            height: 16px;
            border-radius: 50%;
            background-color: var(--primary-color);
            border: 2px solid white;
            box-shadow: 0 0 0 2px var(--primary-color);
        }

        .timeline-content {
            background-color: #f9f9f9;
            border-radius: 8px;
            padding: 16px;
        }

        .timeline-time {
            font-size: 12px;
            color: var(--text-light);
            margin-bottom: 8px;
        }

        .timeline-title {
            font-weight: 500;
            margin-bottom: 8px;
        }

        .timeline-description {
            color: var(--text-light);
        }

        /* 筛选和搜索区域 */
        .filter-container {
            display: flex;
            gap: 16px;
            flex-wrap: wrap;
            background-color: white;
            padding: 16px;
            border-radius: 8px;
            box-shadow: 0 2px 8px rgba(0,0,0,0.08);
            margin-bottom: 24px;
        }

        /* 能耗优化建议卡片 */
        .optimization-card {
            background: white;
            border-radius: 12px;
            padding: 20px;
            box-shadow: 0 4px 12px rgba(0,0,0,0.08);
            margin-bottom: 20px;
            border-left: 4px solid var(--primary-color);
        }

        .optimization-header {
            display: flex;
            align-items: center;
            gap: 12px;
            margin-bottom: 16px;
        }

        .optimization-icon {
            width: 40px;
            height: 40px;
            border-radius: 50%;
            background-color: #e6f7ff;
            display: flex;
            align-items: center;
            justify-content: center;
            color: var(--primary-color);
            font-size: 20px;
        }

        .optimization-title {
            font-size: 16px;
            font-weight: 600;
        }

        .optimization-description {
            color: var(--text-light);
            margin-bottom: 16px;
        }

        .optimization-data {
            display: flex;
            gap: 24px;
            margin-bottom: 16px;
        }

        .data-item {
            display: flex;
            flex-direction: column;
        }

        .data-label {
            font-size: 12px;
            color: var(--text-light);
            margin-bottom: 4px;
        }

        .data-value {
            font-size: 18px;
            font-weight: 600;
        }

        .data-unit {
            font-size: 12px;
            color: var(--text-light);
        }

        /* 动画效果 */
        @keyframes pulse {
            0% {
                transform: scale(1);
                opacity: 1;
            }
            50% {
                transform: scale(1.2);
                opacity: 0.5;
            }
            100% {
                transform: scale(1);
                opacity: 0;
            }
        }

        /* 子菜单样式 */
        .submenu {
            display: flex;
            flex-wrap: wrap;
            gap: 10px;
            margin-bottom: 20px;
            background-color: #fff;
            padding: 15px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);
        }

        .submenu-item {
            padding: 8px 16px;
            border-radius: 6px;
            cursor: pointer;
            transition: all 0.3s;
            display: flex;
            align-items: center;
            gap: 8px;
            font-size: 14px;
            color: #666;
        }

        .submenu-item:hover {
            background-color: #f5f7fa;
            color: var(--primary-color);
        }

        .submenu-item.active {
            background-color: var(--primary-color);
            color: white;
        }

        /* 状态卡片样式 */
        .status-card {
            background-color: #fff;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);
            padding: 20px;
            margin-bottom: 20px;
        }

        .status-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 15px;
            padding-bottom: 10px;
            border-bottom: 1px solid #f0f0f0;
        }

        .status-title {
            font-size: 16px;
            font-weight: 600;
            color: #333;
            margin: 0;
        }

        .status-content {
            display: flex;
            flex-wrap: wrap;
            gap: 15px;
        }

        .status-item {
            flex: 1;
            min-width: 200px;
            padding: 15px;
            background-color: #f9f9f9;
            border-radius: 6px;
        }

        .status-label {
            font-size: 14px;
            color: #666;
            margin-bottom: 5px;
        }

        .status-value {
            font-size: 18px;
            font-weight: 600;
            color: #333;
        }

        /* 仪表板网格样式 */
        .dashboard-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 20px;
            margin-bottom: 20px;
        }

        .dashboard-card {
            background-color: #fff;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);
            padding: 20px;
            transition: all 0.3s;
            position: relative;
            overflow: hidden;
        }

        .dashboard-card:hover {
            box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
            transform: translateY(-2px);
        }

        .dashboard-card::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 4px;
            background: linear-gradient(90deg, var(--primary-color), var(--secondary-color));
        }

        .card-header {
            display: flex;
            align-items: center;
            margin-bottom: 15px;
        }

        .card-icon {
            width: 40px;
            height: 40px;
            border-radius: 8px;
            display: flex;
            align-items: center;
            justify-content: center;
            margin-right: 15px;
            font-size: 18px;
            color: white;
        }

        .card-title {
            font-size: 16px;
            font-weight: 600;
            color: #333;
            margin: 0;
        }

        .card-content {
            color: #666;
            font-size: 14px;
            line-height: 1.5;
        }

        .card-footer {
            margin-top: 15px;
            display: flex;
            justify-content: flex-end;
        }

        .card-link {
            color: var(--primary-color);
            text-decoration: none;
            font-size: 14px;
            font-weight: 500;
            display: flex;
            align-items: center;
        }

        .card-link i {
            margin-left: 5px;
            transition: transform 0.2s;
        }

        .card-link:hover i {
            transform: translateX(3px);
        }

        .bg-blue { background-color: #1890ff; }
        .bg-green { background-color: #52c41a; }
        .bg-orange { background-color: #fa8c16; }
        .bg-purple { background-color: #722ed1; }
        .bg-cyan { background-color: #13c2c2; }
        .bg-red { background-color: #f5222d; }

        /* 编组显示样式 */
        .grouping-display {
            background-color: #f9f9f9;
            border-radius: 6px;
            padding: 15px;
            margin-top: 10px;
        }

        .group-item {
            display: flex;
            justify-content: space-between;
            margin-bottom: 8px;
            padding: 8px 0;
            border-bottom: 1px solid #e8e8e8;
        }

        .group-item:last-child {
            border-bottom: none;
            margin-bottom: 0;
        }

        .group-label {
            font-weight: 500;
            color: #666;
        }

        .group-members {
            color: #333;
            font-weight: 500;
        }

        /* 轮廓按钮样式 */
        .btn-outline {
            background-color: transparent;
            border: 1px solid #d9d9d9;
            color: #666;
            padding: 7px 15px;
            border-radius: 4px;
            cursor: pointer;
            transition: all 0.3s;
        }

        .btn-outline:hover {
            border-color: var(--primary-color);
            color: var(--primary-color);
        }

        /* 响应式调整 */
        @media (max-width: 768px) {
            .control-panel, .dashboard-grid {
                grid-template-columns: 1fr;
            }

            .page-header {
                flex-direction: column;
                align-items: flex-start;
            }

            .header-actions {
                width: 100%;
                justify-content: flex-end;
            }

            .submenu {
                overflow-x: auto;
                padding: 10px;
                flex-wrap: nowrap;
            }

            .submenu-item {
                white-space: nowrap;
            }

            .status-content {
                flex-direction: column;
            }
        }
    </style>
</head>
<body>
<script>document.addEventListener("DOMContentLoaded", function() { loadCommonElements(); });</script>
    <div class="container">
        <!-- 顶部栏 -->
        <div class="topbar">
            <div class="topbar-left">
                <div class="toggle-sidebar" onclick="toggleSidebar()">
                    <i class="fas fa-bars"></i>
                </div>
                <img src="logo.png" alt="公司Logo" class="topbar-logo" style="height: 60px; margin-right: 15px; transition: transform 0.3s;" onmouseover="this.style.transform='scale(1.05)'" onmouseout="this.style.transform='scale(1)'">
                <h2 class="topbar-title">精准曝气控制系统</h2>
            </div>
            <div class="topbar-right">
                <div class="user-dropdown">
                    <div class="user-info">
                        <div class="user-avatar">管</div>
                        <span>管理员</span>
                        <i class="fas fa-chevron-down" style="margin-left: 5px; font-size: 12px;"></i>
                    </div>
                    <div class="dropdown-content">
                        <a href="#"><i class="fas fa-user-circle"></i> 个人信息</a>
                        <a href="#"><i class="fas fa-cog"></i> 账号设置</a>
                        <a href="index.html" onclick="logout()"><i class="fas fa-sign-out-alt"></i> 退出登录</a>
                    </div>
                </div>
            </div>
        </div>

        <!-- 主体内容区域 -->
        <div class="main-container">
            <!-- 侧边栏 -->
            <div class="sidebar">
                <div class="sidebar-menu">
                    <div class="menu-item" id="menu-dashboard" onclick="window.location.href='dashboard.html'">
                        <i class="fas fa-tachometer-alt"></i>
                        <span>实时监控</span>
                    </div>
                    <div class="menu-item" id="menu-model" onclick="window.location.href='model-control.html'">
                        <i class="fas fa-brain"></i>
                        <span>模型控制</span>
                    </div>
                    <div class="menu-item active" id="menu-scheduling" onclick="window.location.href='scheduling.html'">
                        <i class="fas fa-tasks"></i>
                        <span>调度管理</span>
                    </div>
                    <div class="menu-item" id="menu-reports" onclick="window.location.href='reports.html'">
                        <i class="fas fa-chart-bar"></i>
                        <span>数据报表</span>
                    </div>
                    <div class="menu-item" id="menu-alarms" onclick="window.location.href='alarms.html'">
                        <i class="fas fa-bell"></i>
                        <span>告警管理</span>
                    </div>
                    <div class="menu-item" id="menu-settings" onclick="window.location.href='settings.html'">
                        <i class="fas fa-cog"></i>
                        <span>系统配置</span>
                    </div>
                </div>
            </div>

            <!-- 内容区域 -->
            <div class="content">
                <!-- 子菜单导航 -->
                <div class="submenu">
                    <div class="submenu-item active" onclick="window.location.href='scheduling.html'">
                        <i class="fas fa-home"></i> 调度总览
                    </div>
                    <div class="submenu-item" onclick="window.location.href='device-scheduling.html'">
                        <i class="fas fa-fan"></i> 曝气设备调度
                    </div>
                    <div class="submenu-item" onclick="showBlowerScheduling()">
                        <i class="fas fa-wind"></i> 鼓风机调度
                    </div>
                    <div class="submenu-item" onclick="window.location.href='do-parameter-scheduling.html'">
                        <i class="fas fa-sliders-h"></i> DO控制参数调度
                    </div>
                    <div class="submenu-item" onclick="window.location.href='energy-optimization.html'">
                        <i class="fas fa-bolt"></i> 能源优化调度
                    </div>
                    <div class="submenu-item" onclick="window.location.href='plan-management.html'">
                        <i class="fas fa-calendar-alt"></i> 调度计划管理
                    </div>
                    <div class="submenu-item" onclick="window.location.href='execution-monitoring.html'">
                        <i class="fas fa-desktop"></i> 执行监控
                    </div>
                    <div class="submenu-item" onclick="window.location.href='statistics-reports.html'">
                        <i class="fas fa-chart-pie"></i> 统计报表
                    </div>
                </div>

                <!-- 当前状态概览 -->
                <div class="status-card">
                    <div class="status-header">
                        <h3 class="status-title">当前调度状态</h3>
                        <button class="btn btn-outline" onclick="refreshStatus()">
                            <i class="fas fa-sync-alt"></i> 刷新
                        </button>
                    </div>
                    <div class="status-content">
                        <div class="status-item">
                            <div class="status-label">当前执行计划</div>
                            <div class="status-value">日常运行计划</div>
                        </div>
                        <div class="status-item">
                            <div class="status-label">运行状态</div>
                            <div class="status-value">
                                <div class="status-indicator status-normal"></div>正常
                            </div>
                        </div>
                        <div class="status-item">
                            <div class="status-label">当前DO目标值</div>
                            <div class="status-value">2.0 mg/L</div>
                        </div>
                        <div class="status-item">
                            <div class="status-label">当前能耗</div>
                            <div class="status-value">45.2 kWh</div>
                        </div>
                    </div>
                </div>

                <!-- 功能卡片 -->
                <div class="dashboard-grid">
                    <!-- 曝气设备调度 -->
                    <div class="dashboard-card">
                        <div class="card-header">
                            <div class="card-icon bg-blue">
                                <i class="fas fa-fan"></i>
                            </div>
                            <h3 class="card-title">曝气设备调度</h3>
                        </div>
                        <div class="card-content">
                            管理鼓风机运行计划、设备轮换策略和备用切换设置，确保设备高效运行。
                        </div>
                        <div class="card-footer">
                            <a href="device-scheduling.html" class="card-link">
                                进入管理 <i class="fas fa-arrow-right"></i>
                            </a>
                        </div>
                    </div>

                    <!-- 鼓风机调度 -->
                    <div class="dashboard-card">
                        <div class="card-header">
                            <div class="card-icon bg-cyan">
                                <i class="fas fa-wind"></i>
                            </div>
                            <h3 class="card-title">鼓风机调度</h3>
                        </div>
                        <div class="card-content">
                            智能调度鼓风机运行策略，优化气量分配，实现节能高效的曝气控制。
                        </div>
                        <div class="card-footer">
                            <a href="#" onclick="showBlowerDetailedManagement(); return false;" class="card-link">
                                进入管理 <i class="fas fa-arrow-right"></i>
                            </a>
                        </div>
                    </div>

                    <!-- DO控制参数调度 -->
                    <div class="dashboard-card">
                        <div class="card-header">
                            <div class="card-icon bg-green">
                                <i class="fas fa-sliders-h"></i>
                            </div>
                            <h3 class="card-title">DO控制参数调度</h3>
                        </div>
                        <div class="card-content">
                            设置不同时段的DO目标值、控制参数和日/夜模式切换，优化水质控制效果。
                        </div>
                        <div class="card-footer">
                            <a href="do-parameter-scheduling.html" class="card-link">
                                进入管理 <i class="fas fa-arrow-right"></i>
                            </a>
                        </div>
                    </div>

                    <!-- 能源优化调度 -->
                    <div class="dashboard-card">
                        <div class="card-header">
                            <div class="card-icon bg-orange">
                                <i class="fas fa-bolt"></i>
                            </div>
                            <h3 class="card-title">能源优化调度</h3>
                        </div>
                        <div class="card-content">
                            根据峰谷电价制定节能策略，实现最小能耗运行，控制能源预算。
                        </div>
                        <div class="card-footer">
                            <a href="energy-optimization.html" class="card-link">
                                进入管理 <i class="fas fa-arrow-right"></i>
                            </a>
                        </div>
                    </div>

                    <!-- 调度计划管理 -->
                    <div class="dashboard-card">
                        <div class="card-header">
                            <div class="card-icon bg-purple">
                                <i class="fas fa-calendar-alt"></i>
                            </div>
                            <h3 class="card-title">调度计划管理</h3>
                        </div>
                        <div class="card-content">
                            创建和编辑调度计划，使用计划模板，灵活控制计划的启用状态。
                        </div>
                        <div class="card-footer">
                            <a href="plan-management.html" class="card-link">
                                进入管理 <i class="fas fa-arrow-right"></i>
                            </a>
                        </div>
                    </div>

                    <!-- 执行监控 -->
                    <div class="dashboard-card">
                        <div class="card-header">
                            <div class="card-icon bg-cyan">
                                <i class="fas fa-desktop"></i>
                            </div>
                            <h3 class="card-title">执行监控</h3>
                        </div>
                        <div class="card-content">
                            监控计划执行状态，查看执行历史，必要时进行手动干预。
                        </div>
                        <div class="card-footer">
                            <a href="execution-monitoring.html" class="card-link">
                                进入管理 <i class="fas fa-arrow-right"></i>
                            </a>
                        </div>
                    </div>

                    <!-- 统计报表 -->
                    <div class="dashboard-card">
                        <div class="card-header">
                            <div class="card-icon bg-red">
                                <i class="fas fa-chart-pie"></i>
                            </div>
                            <h3 class="card-title">统计报表</h3>
                        </div>
                        <div class="card-content">
                            查看设备运行统计、能耗统计和调度效果评估，获取优化建议。
                        </div>
                        <div class="card-footer">
                            <a href="statistics-reports.html" class="card-link">
                                进入管理 <i class="fas fa-arrow-right"></i>
                            </a>
                        </div>
                    </div>
                </div>

                <!-- 鼓风机调度详细界面 -->
                <div id="blower-scheduling-panel" style="display: none;">
                    <div class="page-header">
                        <div class="page-title">
                            <i class="fas fa-wind"></i>
                            鼓风机调度管理
                        </div>
                        <div class="header-actions">
                            <button class="btn btn-outline" onclick="hideBlowerScheduling()">
                                <i class="fas fa-arrow-left"></i> 返回总览
                            </button>
                            <button class="btn btn-primary" onclick="saveBlowerSchedule()">
                                <i class="fas fa-save"></i> 保存配置
                            </button>
                        </div>
                    </div>

                    <!-- 鼓风机状态监控 -->
                    <div class="status-card">
                        <div class="status-header">
                            <h3 class="status-title">鼓风机运行状态</h3>
                            <button class="btn btn-outline" onclick="refreshBlowerStatus()">
                                <i class="fas fa-sync-alt"></i> 刷新
                            </button>
                        </div>
                        <div class="status-content">
                            <div class="status-item">
                                <div class="status-label">运行中鼓风机</div>
                                <div class="status-value">3/4 台</div>
                            </div>
                            <div class="status-item">
                                <div class="status-label">总气量输出</div>
                                <div class="status-value">1250 m³/h</div>
                            </div>
                            <div class="status-item">
                                <div class="status-label">平均负载率</div>
                                <div class="status-value">75%</div>
                            </div>
                            <div class="status-item">
                                <div class="status-label">当前能耗</div>
                                <div class="status-value">32.5 kW</div>
                            </div>
                        </div>
                    </div>

                    <!-- 鼓风机控制面板 -->
                    <div class="control-panel">
                        <!-- 鼓风机1 -->
                        <div class="control-card">
                            <div class="card-header">
                                <div class="card-title">
                                    <i class="fas fa-wind"></i>
                                    鼓风机 #1
                                </div>
                                <div class="device-status">
                                    <div class="status-dot status-running"></div>
                                    <span>运行中</span>
                                </div>
                            </div>
                            <div class="info-row">
                                <span>当前转速:</span>
                                <span class="info-value">1450 rpm</span>
                            </div>
                            <div class="info-row">
                                <span>气量输出:</span>
                                <span class="info-value">320 m³/h</span>
                            </div>
                            <div class="info-row">
                                <span>功率:</span>
                                <span class="info-value">8.2 kW</span>
                            </div>
                            <div class="slider-container">
                                <div class="slider-header">
                                    <span>转速设定</span>
                                    <span class="slider-value">75%</span>
                                </div>
                                <input type="range" class="slider" min="30" max="100" value="75"
                                       oninput="updateBlowerSpeed(1, this.value)">
                            </div>
                            <div class="control-buttons">
                                <button class="btn btn-secondary" onclick="stopBlower(1)">停止</button>
                                <button class="btn btn-primary" onclick="startBlower(1)">启动</button>
                            </div>
                        </div>

                        <!-- 鼓风机2 -->
                        <div class="control-card">
                            <div class="card-header">
                                <div class="card-title">
                                    <i class="fas fa-wind"></i>
                                    鼓风机 #2
                                </div>
                                <div class="device-status">
                                    <div class="status-dot status-running"></div>
                                    <span>运行中</span>
                                </div>
                            </div>
                            <div class="info-row">
                                <span>当前转速:</span>
                                <span class="info-value">1380 rpm</span>
                            </div>
                            <div class="info-row">
                                <span>气量输出:</span>
                                <span class="info-value">295 m³/h</span>
                            </div>
                            <div class="info-row">
                                <span>功率:</span>
                                <span class="info-value">7.8 kW</span>
                            </div>
                            <div class="slider-container">
                                <div class="slider-header">
                                    <span>转速设定</span>
                                    <span class="slider-value">70%</span>
                                </div>
                                <input type="range" class="slider" min="30" max="100" value="70"
                                       oninput="updateBlowerSpeed(2, this.value)">
                            </div>
                            <div class="control-buttons">
                                <button class="btn btn-secondary" onclick="stopBlower(2)">停止</button>
                                <button class="btn btn-primary" onclick="startBlower(2)">启动</button>
                            </div>
                        </div>

                        <!-- 鼓风机3 -->
                        <div class="control-card">
                            <div class="card-header">
                                <div class="card-title">
                                    <i class="fas fa-wind"></i>
                                    鼓风机 #3
                                </div>
                                <div class="device-status">
                                    <div class="status-dot status-running"></div>
                                    <span>运行中</span>
                                </div>
                            </div>
                            <div class="info-row">
                                <span>当前转速:</span>
                                <span class="info-value">1520 rpm</span>
                            </div>
                            <div class="info-row">
                                <span>气量输出:</span>
                                <span class="info-value">340 m³/h</span>
                            </div>
                            <div class="info-row">
                                <span>功率:</span>
                                <span class="info-value">8.5 kW</span>
                            </div>
                            <div class="slider-container">
                                <div class="slider-header">
                                    <span>转速设定</span>
                                    <span class="slider-value">80%</span>
                                </div>
                                <input type="range" class="slider" min="30" max="100" value="80"
                                       oninput="updateBlowerSpeed(3, this.value)">
                            </div>
                            <div class="control-buttons">
                                <button class="btn btn-secondary" onclick="stopBlower(3)">停止</button>
                                <button class="btn btn-primary" onclick="startBlower(3)">启动</button>
                            </div>
                        </div>

                        <!-- 鼓风机4 -->
                        <div class="control-card">
                            <div class="card-header">
                                <div class="card-title">
                                    <i class="fas fa-wind"></i>
                                    鼓风机 #4
                                </div>
                                <div class="device-status">
                                    <div class="status-dot status-stopped"></div>
                                    <span>备用</span>
                                </div>
                            </div>
                            <div class="info-row">
                                <span>当前转速:</span>
                                <span class="info-value">0 rpm</span>
                            </div>
                            <div class="info-row">
                                <span>气量输出:</span>
                                <span class="info-value">0 m³/h</span>
                            </div>
                            <div class="info-row">
                                <span>功率:</span>
                                <span class="info-value">0 kW</span>
                            </div>
                            <div class="slider-container">
                                <div class="slider-header">
                                    <span>转速设定</span>
                                    <span class="slider-value">0%</span>
                                </div>
                                <input type="range" class="slider" min="30" max="100" value="0"
                                       oninput="updateBlowerSpeed(4, this.value)" disabled>
                            </div>
                            <div class="control-buttons">
                                <button class="btn btn-secondary" onclick="stopBlower(4)" disabled>停止</button>
                                <button class="btn btn-primary" onclick="startBlower(4)">启动</button>
                            </div>
                        </div>
                    </div>

                    <!-- 调度策略配置 -->
                    <div class="schedule-card">
                        <div class="schedule-header">
                            <div class="schedule-title">智能调度策略</div>
                        </div>
                        <div class="form-group">
                            <label class="form-label">调度模式</label>
                            <select class="form-control" onchange="changeScheduleMode(this.value)">
                                <option value="auto">自动调度</option>
                                <option value="manual">手动调度</option>
                                <option value="energy">节能优先</option>
                                <option value="performance">性能优先</option>
                            </select>
                        </div>
                        <div class="form-group">
                            <label class="form-label">轮换策略</label>
                            <select class="form-control">
                                <option value="time">定时轮换 (每8小时)</option>
                                <option value="load">负载均衡轮换</option>
                                <option value="maintenance">维护计划轮换</option>
                            </select>
                        </div>
                        <div class="form-group">
                            <label class="form-label">目标气量</label>
                            <input type="number" class="form-control" value="1200" min="800" max="2000">
                            <small>单位: m³/h</small>
                        </div>
                    </div>
                </div>

                <!-- 鼓风机详细管理界面 -->
                <div id="blower-detailed-management-panel" style="display: none;">
                    <div class="page-header">
                        <div class="page-title">
                            <i class="fas fa-cogs"></i>
                            鼓风机智能控制系统
                        </div>
                        <div class="header-actions">
                            <button class="btn btn-outline" onclick="hideBlowerDetailedManagement()">
                                <i class="fas fa-arrow-left"></i> 返回调度
                            </button>
                            <button class="btn btn-primary" onclick="saveAllControlSettings()">
                                <i class="fas fa-save"></i> 保存所有配置
                            </button>
                        </div>
                    </div>

                    <!-- 控制模块标签页 -->
                    <div class="tab-container">
                        <div class="tabs">
                            <div class="tab active" onclick="switchControlTab('airflow-balance')">气量平衡控制</div>
                            <div class="tab" onclick="switchControlTab('pressure-balance')">压力平衡控制</div>
                            <div class="tab" onclick="switchControlTab('seamless-switching')">风机无扰切换</div>
                            <div class="tab" onclick="switchControlTab('intelligent-grouping')">智能编组控制</div>
                            <div class="tab" onclick="switchControlTab('surge-protection')">防喘振控制</div>
                        </div>

                        <!-- 气量平衡控制模块 -->
                        <div id="airflow-balance" class="tab-content active">
                            <div class="control-card">
                                <div class="card-header">
                                    <div class="card-title">
                                        <i class="fas fa-balance-scale"></i>
                                        气量平衡控制
                                    </div>
                                    <div class="device-status">
                                        <div class="status-dot status-running"></div>
                                        <span>自动控制中</span>
                                    </div>
                                </div>
                                <div class="form-group">
                                    <label class="form-label">目标总气量</label>
                                    <input type="number" class="form-control" value="1200" min="800" max="2000">
                                    <small>单位: m³/h</small>
                                </div>
                                <div class="form-group">
                                    <label class="form-label">气量分配策略</label>
                                    <select class="form-control" onchange="changeAirflowStrategy(this.value)">
                                        <option value="equal">均匀分配</option>
                                        <option value="load-based" selected>负载优化分配</option>
                                        <option value="efficiency">效率优先分配</option>
                                        <option value="custom">自定义分配</option>
                                    </select>
                                </div>
                                <div class="form-group">
                                    <label class="form-label">平衡精度</label>
                                    <input type="range" class="slider" min="1" max="10" value="5"
                                           oninput="updateBalancePrecision(this.value)">
                                    <div class="slider-header">
                                        <span>低精度</span>
                                        <span class="slider-value">中等精度</span>
                                        <span>高精度</span>
                                    </div>
                                </div>
                                <div class="status-content">
                                    <div class="status-item">
                                        <div class="status-label">当前偏差</div>
                                        <div class="status-value">±2.3%</div>
                                    </div>
                                    <div class="status-item">
                                        <div class="status-label">调节次数</div>
                                        <div class="status-value">15次/小时</div>
                                    </div>
                                    <div class="status-item">
                                        <div class="status-label">平衡状态</div>
                                        <div class="status-value">良好</div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- 压力平衡控制模块 -->
                        <div id="pressure-balance" class="tab-content">
                            <div class="control-card">
                                <div class="card-header">
                                    <div class="card-title">
                                        <i class="fas fa-tachometer-alt"></i>
                                        压力平衡控制
                                    </div>
                                    <div class="device-status">
                                        <div class="status-dot status-running"></div>
                                        <span>压力稳定</span>
                                    </div>
                                </div>
                                <div class="form-group">
                                    <label class="form-label">目标压力</label>
                                    <input type="number" class="form-control" value="45" min="20" max="80" step="0.1">
                                    <small>单位: kPa</small>
                                </div>
                                <div class="form-group">
                                    <label class="form-label">压力控制模式</label>
                                    <select class="form-control" onchange="changePressureMode(this.value)">
                                        <option value="constant">恒压控制</option>
                                        <option value="variable" selected>变压控制</option>
                                        <option value="adaptive">自适应控制</option>
                                    </select>
                                </div>
                                <div class="form-group">
                                    <label class="form-label">压力波动限制</label>
                                    <input type="range" class="slider" min="1" max="10" value="3"
                                           oninput="updatePressureLimit(this.value)">
                                    <div class="slider-header">
                                        <span>±1kPa</span>
                                        <span class="slider-value">±3kPa</span>
                                        <span>±10kPa</span>
                                    </div>
                                </div>
                                <div class="status-content">
                                    <div class="status-item">
                                        <div class="status-label">当前压力</div>
                                        <div class="status-value">44.8 kPa</div>
                                    </div>
                                    <div class="status-item">
                                        <div class="status-label">压力偏差</div>
                                        <div class="status-value">-0.2 kPa</div>
                                    </div>
                                    <div class="status-item">
                                        <div class="status-label">稳定性</div>
                                        <div class="status-value">优秀</div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- 风机无扰切换控制模块 -->
                        <div id="seamless-switching" class="tab-content">
                            <div class="control-card">
                                <div class="card-header">
                                    <div class="card-title">
                                        <i class="fas fa-exchange-alt"></i>
                                        风机无扰切换控制
                                    </div>
                                    <div class="device-status">
                                        <div class="status-dot status-idle"></div>
                                        <span>待机状态</span>
                                    </div>
                                </div>
                                <div class="form-group">
                                    <label class="form-label">切换策略</label>
                                    <select class="form-control" onchange="changeSwitchingStrategy(this.value)">
                                        <option value="gradual" selected>渐进式切换</option>
                                        <option value="overlap">重叠式切换</option>
                                        <option value="instant">瞬时切换</option>
                                    </select>
                                </div>
                                <div class="form-group">
                                    <label class="form-label">切换时间</label>
                                    <input type="number" class="form-control" value="30" min="10" max="120">
                                    <small>单位: 秒</small>
                                </div>
                                <div class="form-group">
                                    <label class="form-label">压力波动阈值</label>
                                    <input type="number" class="form-control" value="2" min="0.5" max="5" step="0.1">
                                    <small>单位: kPa</small>
                                </div>
                                <div class="control-buttons">
                                    <button class="btn btn-primary" onclick="testSeamlessSwitching()">测试切换</button>
                                    <button class="btn btn-secondary" onclick="viewSwitchingHistory()">切换历史</button>
                                </div>
                                <div class="status-content">
                                    <div class="status-item">
                                        <div class="status-label">上次切换</div>
                                        <div class="status-value">2小时前</div>
                                    </div>
                                    <div class="status-item">
                                        <div class="status-label">切换成功率</div>
                                        <div class="status-value">98.5%</div>
                                    </div>
                                    <div class="status-item">
                                        <div class="status-label">平均切换时间</div>
                                        <div class="status-value">28秒</div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- 风机智能编组控制模块 -->
                        <div id="intelligent-grouping" class="tab-content">
                            <div class="control-card">
                                <div class="card-header">
                                    <div class="card-title">
                                        <i class="fas fa-sitemap"></i>
                                        风机智能编组控制
                                    </div>
                                    <div class="device-status">
                                        <div class="status-dot status-running"></div>
                                        <span>智能编组中</span>
                                    </div>
                                </div>
                                <div class="form-group">
                                    <label class="form-label">编组策略</label>
                                    <select class="form-control" onchange="changeGroupingStrategy(this.value)">
                                        <option value="load-balance" selected>负载均衡编组</option>
                                        <option value="efficiency">效率优先编组</option>
                                        <option value="maintenance">维护计划编组</option>
                                        <option value="manual">手动编组</option>
                                    </select>
                                </div>
                                <div class="form-group">
                                    <label class="form-label">当前编组配置</label>
                                    <div class="grouping-display">
                                        <div class="group-item">
                                            <span class="group-label">主组:</span>
                                            <span class="group-members">风机#1, 风机#2</span>
                                        </div>
                                        <div class="group-item">
                                            <span class="group-label">备用组:</span>
                                            <span class="group-members">风机#3, 风机#4</span>
                                        </div>
                                    </div>
                                </div>
                                <div class="form-group">
                                    <label class="form-label">轮换周期</label>
                                    <select class="form-control">
                                        <option value="4">4小时</option>
                                        <option value="8" selected>8小时</option>
                                        <option value="12">12小时</option>
                                        <option value="24">24小时</option>
                                    </select>
                                </div>
                                <div class="control-buttons">
                                    <button class="btn btn-primary" onclick="optimizeGrouping()">优化编组</button>
                                    <button class="btn btn-secondary" onclick="manualGrouping()">手动编组</button>
                                </div>
                                <div class="status-content">
                                    <div class="status-item">
                                        <div class="status-label">编组效率</div>
                                        <div class="status-value">92.3%</div>
                                    </div>
                                    <div class="status-item">
                                        <div class="status-label">负载均衡度</div>
                                        <div class="status-value">95.1%</div>
                                    </div>
                                    <div class="status-item">
                                        <div class="status-label">下次轮换</div>
                                        <div class="status-value">6小时后</div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- 风机防喘振控制模块 -->
                        <div id="surge-protection" class="tab-content">
                            <div class="control-card">
                                <div class="card-header">
                                    <div class="card-title">
                                        <i class="fas fa-shield-alt"></i>
                                        风机防喘振控制
                                    </div>
                                    <div class="device-status">
                                        <div class="status-dot status-running"></div>
                                        <span>保护激活</span>
                                    </div>
                                </div>
                                <div class="form-group">
                                    <label class="form-label">防喘振策略</label>
                                    <select class="form-control" onchange="changeSurgeProtection(this.value)">
                                        <option value="conservative" selected>保守策略</option>
                                        <option value="balanced">平衡策略</option>
                                        <option value="aggressive">激进策略</option>
                                    </select>
                                </div>
                                <div class="form-group">
                                    <label class="form-label">喘振检测灵敏度</label>
                                    <input type="range" class="slider" min="1" max="10" value="7"
                                           oninput="updateSurgeSensitivity(this.value)">
                                    <div class="slider-header">
                                        <span>低</span>
                                        <span class="slider-value">高</span>
                                        <span>极高</span>
                                    </div>
                                </div>
                                <div class="form-group">
                                    <label class="form-label">最小安全流量</label>
                                    <input type="number" class="form-control" value="200" min="100" max="500">
                                    <small>单位: m³/h</small>
                                </div>
                                <div class="form-group">
                                    <label class="form-label">防喘振阀开度</label>
                                    <input type="range" class="slider" min="0" max="100" value="15"
                                           oninput="updateSurgeValve(this.value)">
                                    <div class="slider-header">
                                        <span>0%</span>
                                        <span class="slider-value">15%</span>
                                        <span>100%</span>
                                    </div>
                                </div>
                                <div class="status-content">
                                    <div class="status-item">
                                        <div class="status-label">喘振风险</div>
                                        <div class="status-value">低</div>
                                    </div>
                                    <div class="status-item">
                                        <div class="status-label">保护动作次数</div>
                                        <div class="status-value">3次/天</div>
                                    </div>
                                    <div class="status-item">
                                        <div class="status-label">系统稳定性</div>
                                        <div class="status-value">优秀</div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
        </div>
    </div>

    <script src="js/common.js"></script>
    <script>
        // 刷新状态功能

        // 刷新状态
        function refreshStatus() {
            showNotification('正在刷新调度状态...', 'info');

            // 模拟加载延迟
            setTimeout(function() {
                showNotification('调度状态已更新', 'success');
            }, 1000);
        }

        // 鼓风机调度相关函数

        // 显示鼓风机调度界面
        function showBlowerScheduling() {
            // 隐藏主界面内容
            const mainContent = document.querySelector('.dashboard-grid').parentElement;
            const statusCard = document.querySelector('.status-card');
            const submenu = document.querySelector('.submenu');

            mainContent.style.display = 'none';
            statusCard.style.display = 'none';

            // 显示鼓风机调度面板
            document.getElementById('blower-scheduling-panel').style.display = 'block';

            // 更新子菜单激活状态
            document.querySelectorAll('.submenu-item').forEach(item => {
                item.classList.remove('active');
            });
            document.querySelector('.submenu-item[onclick="showBlowerScheduling()"]').classList.add('active');

            showNotification('已进入鼓风机调度管理', 'info');
        }

        // 隐藏鼓风机调度界面
        function hideBlowerScheduling() {
            // 显示主界面内容
            const mainContent = document.querySelector('.dashboard-grid').parentElement;
            const statusCard = document.querySelector('.status-card');

            mainContent.style.display = 'block';
            statusCard.style.display = 'block';

            // 隐藏鼓风机调度面板
            document.getElementById('blower-scheduling-panel').style.display = 'none';

            // 恢复子菜单激活状态
            document.querySelectorAll('.submenu-item').forEach(item => {
                item.classList.remove('active');
            });
            document.querySelector('.submenu-item[onclick="window.location.href=\'scheduling.html\'"]').classList.add('active');

            showNotification('已返回调度总览', 'info');
        }

        // 刷新鼓风机状态
        function refreshBlowerStatus() {
            showNotification('正在刷新鼓风机状态...', 'info');

            setTimeout(function() {
                showNotification('鼓风机状态已更新', 'success');
            }, 1000);
        }

        // 更新鼓风机转速
        function updateBlowerSpeed(blowerId, speed) {
            const sliderValue = document.querySelector(`input[oninput="updateBlowerSpeed(${blowerId}, this.value)"]`).parentElement.querySelector('.slider-value');
            sliderValue.textContent = speed + '%';

            // 模拟更新实际转速和气量
            const card = document.querySelector(`input[oninput="updateBlowerSpeed(${blowerId}, this.value)"]`).closest('.control-card');
            const rpmElement = card.querySelector('.info-row:nth-child(2) .info-value');
            const flowElement = card.querySelector('.info-row:nth-child(3) .info-value');
            const powerElement = card.querySelector('.info-row:nth-child(4) .info-value');

            // 计算新的转速、气量和功率
            const maxRpm = 1800;
            const maxFlow = 400;
            const maxPower = 10;

            const newRpm = Math.round(maxRpm * speed / 100);
            const newFlow = Math.round(maxFlow * speed / 100);
            const newPower = (maxPower * Math.pow(speed / 100, 3)).toFixed(1);

            rpmElement.textContent = newRpm + ' rpm';
            flowElement.textContent = newFlow + ' m³/h';
            powerElement.textContent = newPower + ' kW';
        }

        // 启动鼓风机
        function startBlower(blowerId) {
            const card = document.querySelector(`button[onclick="startBlower(${blowerId})"]`).closest('.control-card');
            const statusDot = card.querySelector('.status-dot');
            const statusText = card.querySelector('.device-status span');
            const slider = card.querySelector('.slider');
            const stopBtn = card.querySelector(`button[onclick="stopBlower(${blowerId})"]`);

            statusDot.className = 'status-dot status-running';
            statusText.textContent = '运行中';
            slider.disabled = false;
            stopBtn.disabled = false;

            // 设置默认转速
            if (slider.value == 0) {
                slider.value = 50;
                updateBlowerSpeed(blowerId, 50);
            }

            showNotification(`鼓风机 #${blowerId} 已启动`, 'success');
        }

        // 停止鼓风机
        function stopBlower(blowerId) {
            const card = document.querySelector(`button[onclick="stopBlower(${blowerId})"]`).closest('.control-card');
            const statusDot = card.querySelector('.status-dot');
            const statusText = card.querySelector('.device-status span');
            const slider = card.querySelector('.slider');
            const stopBtn = card.querySelector(`button[onclick="stopBlower(${blowerId})"]`);

            statusDot.className = 'status-dot status-stopped';
            statusText.textContent = '已停止';
            slider.disabled = true;
            stopBtn.disabled = true;

            // 重置转速显示
            slider.value = 0;
            updateBlowerSpeed(blowerId, 0);

            showNotification(`鼓风机 #${blowerId} 已停止`, 'warning');
        }

        // 改变调度模式
        function changeScheduleMode(mode) {
            let modeText = '';
            switch(mode) {
                case 'auto':
                    modeText = '自动调度';
                    break;
                case 'manual':
                    modeText = '手动调度';
                    break;
                case 'energy':
                    modeText = '节能优先';
                    break;
                case 'performance':
                    modeText = '性能优先';
                    break;
            }
            showNotification(`调度模式已切换为: ${modeText}`, 'info');
        }

        // 保存鼓风机调度配置
        function saveBlowerSchedule() {
            showNotification('正在保存鼓风机调度配置...', 'info');

            setTimeout(function() {
                showNotification('鼓风机调度配置已保存', 'success');
            }, 1000);
        }

        // 鼓风机详细管理相关函数

        // 显示鼓风机详细管理界面
        function showBlowerDetailedManagement() {
            // 隐藏主界面内容
            const mainContent = document.querySelector('.dashboard-grid').parentElement;
            const statusCard = document.querySelector('.status-card');

            mainContent.style.display = 'none';
            statusCard.style.display = 'none';

            // 显示详细管理面板
            document.getElementById('blower-detailed-management-panel').style.display = 'block';

            // 更新子菜单激活状态
            document.querySelectorAll('.submenu-item').forEach(item => {
                item.classList.remove('active');
            });
            document.querySelector('.submenu-item[onclick="showBlowerScheduling()"]').classList.add('active');

            showNotification('已进入鼓风机智能控制系统', 'info');
        }

        // 隐藏鼓风机详细管理界面
        function hideBlowerDetailedManagement() {
            // 显示主界面内容
            const mainContent = document.querySelector('.dashboard-grid').parentElement;
            const statusCard = document.querySelector('.status-card');

            mainContent.style.display = 'block';
            statusCard.style.display = 'block';

            // 隐藏详细管理面板
            document.getElementById('blower-detailed-management-panel').style.display = 'none';

            // 恢复子菜单激活状态
            document.querySelectorAll('.submenu-item').forEach(item => {
                item.classList.remove('active');
            });
            document.querySelector('.submenu-item[onclick="window.location.href=\'scheduling.html\'"]').classList.add('active');

            showNotification('已返回调度总览', 'info');
        }

        // 切换控制模块标签页
        function switchControlTab(tabId) {
            // 隐藏所有标签页内容
            const tabContents = document.querySelectorAll('#blower-detailed-management-panel .tab-content');
            tabContents.forEach(content => {
                content.classList.remove('active');
            });

            // 取消所有标签页激活状态
            const tabs = document.querySelectorAll('#blower-detailed-management-panel .tab');
            tabs.forEach(tab => {
                tab.classList.remove('active');
            });

            // 激活选中的标签页和内容
            document.getElementById(tabId).classList.add('active');
            const selectedTab = document.querySelector(`#blower-detailed-management-panel .tab[onclick="switchControlTab('${tabId}')"]`);
            selectedTab.classList.add('active');
        }

        // 气量平衡控制相关函数
        function changeAirflowStrategy(strategy) {
            let strategyText = '';
            switch(strategy) {
                case 'equal':
                    strategyText = '均匀分配';
                    break;
                case 'load-based':
                    strategyText = '负载优化分配';
                    break;
                case 'efficiency':
                    strategyText = '效率优先分配';
                    break;
                case 'custom':
                    strategyText = '自定义分配';
                    break;
            }
            showNotification(`气量分配策略已切换为: ${strategyText}`, 'info');
        }

        function updateBalancePrecision(value) {
            const sliderValue = document.querySelector('#airflow-balance .slider-value');
            let precisionText = '';
            if (value <= 3) {
                precisionText = '低精度';
            } else if (value <= 7) {
                precisionText = '中等精度';
            } else {
                precisionText = '高精度';
            }
            sliderValue.textContent = precisionText;
        }

        // 压力平衡控制相关函数
        function changePressureMode(mode) {
            let modeText = '';
            switch(mode) {
                case 'constant':
                    modeText = '恒压控制';
                    break;
                case 'variable':
                    modeText = '变压控制';
                    break;
                case 'adaptive':
                    modeText = '自适应控制';
                    break;
            }
            showNotification(`压力控制模式已切换为: ${modeText}`, 'info');
        }

        function updatePressureLimit(value) {
            const sliderValue = document.querySelector('#pressure-balance .slider-value');
            sliderValue.textContent = `±${value}kPa`;
        }

        // 风机无扰切换控制相关函数
        function changeSwitchingStrategy(strategy) {
            let strategyText = '';
            switch(strategy) {
                case 'gradual':
                    strategyText = '渐进式切换';
                    break;
                case 'overlap':
                    strategyText = '重叠式切换';
                    break;
                case 'instant':
                    strategyText = '瞬时切换';
                    break;
            }
            showNotification(`切换策略已设置为: ${strategyText}`, 'info');
        }

        function testSeamlessSwitching() {
            showNotification('正在启动无扰切换测试...', 'info');

            setTimeout(function() {
                showNotification('无扰切换测试成功完成', 'success');
            }, 3000);
        }

        function viewSwitchingHistory() {
            showNotification('正在加载切换历史记录...', 'info');

            setTimeout(function() {
                alert('切换历史记录：\n1. 2025-01-15 14:30 - 风机#1→风机#3 - 成功 (28秒)\n2. 2025-01-15 10:15 - 风机#2→风机#4 - 成功 (32秒)\n3. 2025-01-14 22:45 - 风机#3→风机#1 - 成功 (25秒)');
                showNotification('切换历史记录已加载', 'success');
            }, 1000);
        }

        // 风机智能编组控制相关函数
        function changeGroupingStrategy(strategy) {
            let strategyText = '';
            switch(strategy) {
                case 'load-balance':
                    strategyText = '负载均衡编组';
                    break;
                case 'efficiency':
                    strategyText = '效率优先编组';
                    break;
                case 'maintenance':
                    strategyText = '维护计划编组';
                    break;
                case 'manual':
                    strategyText = '手动编组';
                    break;
            }
            showNotification(`编组策略已切换为: ${strategyText}`, 'info');
        }

        function optimizeGrouping() {
            showNotification('正在优化风机编组配置...', 'info');

            setTimeout(function() {
                showNotification('风机编组优化完成，效率提升3.2%', 'success');
            }, 2000);
        }

        function manualGrouping() {
            showNotification('正在打开手动编组界面...', 'info');

            setTimeout(function() {
                alert('手动编组设置：\n请拖拽风机到对应组别\n\n主组: [风机#1] [风机#2]\n备用组: [风机#3] [风机#4]\n\n点击确定保存配置');
                showNotification('手动编组配置已保存', 'success');
            }, 1000);
        }

        // 风机防喘振控制相关函数
        function changeSurgeProtection(strategy) {
            let strategyText = '';
            switch(strategy) {
                case 'conservative':
                    strategyText = '保守策略';
                    break;
                case 'balanced':
                    strategyText = '平衡策略';
                    break;
                case 'aggressive':
                    strategyText = '激进策略';
                    break;
            }
            showNotification(`防喘振策略已设置为: ${strategyText}`, 'info');
        }

        function updateSurgeSensitivity(value) {
            const sliderValue = document.querySelector('#surge-protection .slider-value');
            let sensitivityText = '';
            if (value <= 3) {
                sensitivityText = '低';
            } else if (value <= 7) {
                sensitivityText = '高';
            } else {
                sensitivityText = '极高';
            }
            sliderValue.textContent = sensitivityText;
        }

        function updateSurgeValve(value) {
            const sliderValue = document.querySelector('#surge-protection .slider-value');
            sliderValue.textContent = value + '%';
        }

        // 保存所有控制设置
        function saveAllControlSettings() {
            showNotification('正在保存所有控制模块配置...', 'info');

            setTimeout(function() {
                showNotification('所有控制模块配置已保存', 'success');
            }, 1500);
        }
    </script>
</body>
</html>
