<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>精准曝气控制系统 (GPAC) - 数据报表</title>
    <link rel="stylesheet" href="css/all.min.css">
    <link rel="stylesheet" href="css/style.css">
    <script src="js/echarts.js"></script>
    <style>
        .report-container {
            padding: 20px;
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 20px;
        }
        .report-card {
            background: white;
            border-radius: 8px;
            padding: 20px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        .chart-container {
            height: 300px;
            width: 100%;
        }
        .filter-panel {
            background: white;
            padding: 15px;
            border-radius: 8px;
            margin-bottom: 20px;
            display: flex;
            gap: 15px;
            flex-wrap: wrap;
        }
        .data-table {
            width: 100%;
            border-collapse: collapse;
            margin-top: 15px;
        }
        .data-table th, .data-table td {
            padding: 12px;
            text-align: left;
            border-bottom: 1px solid #eee;
        }
        .data-table th {
            background-color: #f5f5f5;
        }
    </style>
</head>
<body>
<script>document.addEventListener("DOMContentLoaded", function() { loadCommonElements(); });</script>
    <div class="container">
        <!-- 顶部栏 -->
        <div class="topbar">
            <div class="topbar-left">
                <div class="toggle-sidebar" onclick="toggleSidebar()">
                    <i class="fas fa-bars"></i>
                </div>
                <img src="logo.png" alt="公司Logo" class="topbar-logo">
                <h2 class="topbar-title">精准曝气控制系统</h2>
            </div>
            <div class="topbar-right">
                <div class="user-dropdown">
                    <div class="user-info">
                        <div class="user-avatar">管</div>
                        <span>管理员</span>
                        <i class="fas fa-chevron-down" style="margin-left: 5px; font-size: 12px;"></i>
                    </div>
                    <div class="dropdown-content">
                        <a href="#"><i class="fas fa-user-circle"></i> 个人信息</a>
                        <a href="#"><i class="fas fa-cog"></i> 账号设置</a>
                        <a href="index.html" onclick="logout()"><i class="fas fa-sign-out-alt"></i> 退出登录</a>
                    </div>
                </div>
            </div>
        </div>

        <!-- 主体内容区域 -->
        <div class="main-container">
            <!-- 侧边栏 -->
            <div class="sidebar">
                <div class="sidebar-menu">
                    <div class="menu-item" id="menu-dashboard" onclick="window.location.href='dashboard.html'">
                        <i class="fas fa-tachometer-alt"></i>
                        <span>实时监控</span>
                    </div>
                    <div class="menu-item" id="menu-model" onclick="window.location.href='model-control.html'">
                        <i class="fas fa-brain"></i>
                        <span>模型控制</span>
                    </div>
                    <div class="menu-item" id="menu-scheduling" onclick="window.location.href='scheduling.html'">
                        <i class="fas fa-tasks"></i>
                        <span>调度管理</span>
                    </div>
                    <div class="menu-item active" id="menu-reports" onclick="window.location.href='reports.html'">
                        <i class="fas fa-chart-bar"></i>
                        <span>数据报表</span>
                    </div>
                    <div class="menu-item" id="menu-alarms" onclick="window.location.href='alarms.html'">
                        <i class="fas fa-bell"></i>
                        <span>告警管理</span>
                    </div>
                    <div class="menu-item" id="menu-settings" onclick="window.location.href='settings.html'">
                        <i class="fas fa-cog"></i>
                        <span>系统配置</span>
                    </div>
                </div>
            </div>

            <!-- 内容区域 -->
            <div class="content">

            <!-- 筛选面板 -->
            <div class="filter-panel">
                <div class="form-group">
                    <label>时间范围</label>
                    <select class="form-control">
                        <option>今日</option>
                        <option>本周</option>
                        <option>本月</option>
                        <option>自定义</option>
                    </select>
                </div>
                <div class="form-group">
                    <label>区域选择</label>
                    <select class="form-control">
                        <option>全部区域</option>
                        <option>A区</option>
                        <option>B区</option>
                        <option>C区</option>
                    </select>
                </div>
                <div class="form-group">
                    <label>数据类型</label>
                    <select class="form-control">
                        <option>DO浓度</option>
                        <option>NH₄⁺浓度</option>
                        <option>能耗数据</option>
                    </select>
                </div>
                <button class="btn btn-primary">生成报表</button>
                <button class="btn btn-secondary">导出数据</button>
            </div>

            <div class="report-container">
                <!-- 能耗分析卡片 -->
                <div class="report-card">
                    <h3>能耗分析</h3>
                    <div id="energyChart" class="chart-container"></div>
                </div>

                <!-- 水质指标卡片 -->
                <div class="report-card">
                    <h3>水质达标情况</h3>
                    <div id="waterQualityChart" class="chart-container"></div>
                </div>

                <!-- 运行数据表格 -->
                <div class="report-card" style="grid-column: 1 / -1;">
                    <h3>运行数据明细</h3>
                    <table class="data-table">
                        <thead>
                            <tr>
                                <th>时间</th>
                                <th>DO浓度(mg/L)</th>
                                <th>NH₄⁺浓度(mg/L)</th>
                                <th>能耗(kWh)</th>
                                <th>运行状态</th>
                            </tr>
                        </thead>
                        <tbody>
                            <tr>
                                <td>2025-05-16 08:00</td>
                                <td>2.3</td>
                                <td>1.2</td>
                                <td>45.6</td>
                                <td>正常</td>
                            </tr>
                            <tr>
                                <td>2025-05-16 09:00</td>
                                <td>2.1</td>
                                <td>1.3</td>
                                <td>43.2</td>
                                <td>正常</td>
                            </tr>
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>
    </div>

    <script src="js/common.js"></script>
    <script>
        // 初始化图表
        const energyChart = echarts.init(document.getElementById('energyChart'));
        const waterQualityChart = echarts.init(document.getElementById('waterQualityChart'));

        // 能耗分析图表配置
        energyChart.setOption({
            title: {
                text: '日能耗趋势'
            },
            tooltip: {
                trigger: 'axis'
            },
            xAxis: {
                type: 'category',
                data: ['00:00', '04:00', '08:00', '12:00', '16:00', '20:00']
            },
            yAxis: {
                type: 'value',
                name: '能耗(kWh)'
            },
            series: [{
                data: [42, 45, 50, 48, 46, 43],
                type: 'line',
                smooth: true
            }]
        });

        // 水质指标图表配置
        waterQualityChart.setOption({

            tooltip: {
                trigger: 'item'
            },
            legend: {
                orient: 'vertical',
                left: 'left'
            },
            series: [{
                type: 'pie',
                radius: '50%',
                data: [
                    {value: 95, name: 'DO达标'},
                    {value: 92, name: 'NH₄⁺达标'},
                    {value: 8, name: '未达标'}
                ],
                emphasis: {
                    itemStyle: {
                        shadowBlur: 10,
                        shadowOffsetX: 0,
                        shadowColor: 'rgba(0, 0, 0, 0.5)'
                    }
                }
            }]
        });

        // 响应窗口大小变化
        window.addEventListener('resize', function() {
            energyChart.resize();
            waterQualityChart.resize();
        });
    </script>
</body>
</html>
