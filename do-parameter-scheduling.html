<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>精准曝气控制系统 (GPAC) - DO控制参数调度</title>
    <link rel="stylesheet" href="css/all.min.css">
    <link rel="stylesheet" href="css/style.css">
    <style>
        /* 确保内容不被侧边栏遮挡 */
        .content {
            margin-left: var(--sidebar-width) !important;
            width: calc(100% - var(--sidebar-width)) !important;
            padding: 20px !important;
            box-sizing: border-box !important;
        }

        /* 子菜单样式 */
        .submenu {
            display: flex;
            flex-wrap: wrap;
            gap: 10px;
            margin-bottom: 20px;
            background-color: #fff;
            padding: 15px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);
        }

        .submenu-item {
            padding: 8px 16px;
            border-radius: 6px;
            cursor: pointer;
            transition: all 0.3s;
            display: flex;
            align-items: center;
            gap: 8px;
            font-size: 14px;
            color: #666;
        }

        .submenu-item:hover {
            background-color: #f5f7fa;
            color: var(--primary-color);
        }

        .submenu-item.active {
            background-color: var(--primary-color);
            color: white;
        }

        /* 标签页样式 */
        .tab-container {
            background-color: #fff;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);
            margin-bottom: 20px;
            overflow: hidden;
        }

        .tab-header {
            display: flex;
            border-bottom: 1px solid #f0f0f0;
            background-color: #fafafa;
        }

        .tab-item {
            padding: 15px 20px;
            cursor: pointer;
            font-weight: 500;
            color: #666;
            transition: all 0.3s;
            position: relative;
        }

        .tab-item:hover {
            color: var(--primary-color);
        }

        .tab-item.active {
            color: var(--primary-color);
        }

        .tab-item.active::after {
            content: '';
            position: absolute;
            bottom: 0;
            left: 0;
            width: 100%;
            height: 2px;
            background-color: var(--primary-color);
        }

        .tab-content {
            padding: 20px;
            display: none;
        }

        .tab-content.active {
            display: block;
        }

        /* 表单样式 */
        .form-section {
            margin-bottom: 25px;
            padding-bottom: 20px;
            border-bottom: 1px solid #f0f0f0;
        }

        .form-section:last-child {
            border-bottom: none;
            margin-bottom: 0;
            padding-bottom: 0;
        }

        .section-title {
            font-size: 16px;
            font-weight: 600;
            margin-bottom: 15px;
            color: #333;
            display: flex;
            align-items: center;
            gap: 8px;
        }

        .form-row {
            display: flex;
            flex-wrap: wrap;
            gap: 20px;
            margin-bottom: 15px;
        }

        .form-group {
            flex: 1;
            min-width: 250px;
        }

        .form-label {
            display: block;
            margin-bottom: 8px;
            font-size: 14px;
            color: #666;
        }

        .form-control {
            width: 100%;
            padding: 10px 12px;
            border: 1px solid #d9d9d9;
            border-radius: 4px;
            font-size: 14px;
            transition: all 0.3s;
        }

        .form-control:hover {
            border-color: #40a9ff;
        }

        .form-control:focus {
            border-color: #1890ff;
            box-shadow: 0 0 0 2px rgba(24, 144, 255, 0.2);
            outline: none;
        }

        .form-text {
            font-size: 12px;
            color: #999;
            margin-top: 5px;
        }

        /* 时间选择器样式 */
        .time-picker {
            display: flex;
            align-items: center;
            gap: 10px;
        }

        .time-picker .form-control {
            width: auto;
        }

        /* 开关样式 */
        .switch {
            position: relative;
            display: inline-block;
            width: 44px;
            height: 22px;
        }

        .switch input {
            opacity: 0;
            width: 0;
            height: 0;
        }

        .slider {
            position: absolute;
            cursor: pointer;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background-color: #ccc;
            transition: .4s;
            border-radius: 22px;
        }

        .slider:before {
            position: absolute;
            content: "";
            height: 18px;
            width: 18px;
            left: 3px;
            bottom: 2px;
            background-color: white;
            transition: .4s;
            border-radius: 50%;
        }

        input:checked + .slider {
            background-color: var(--primary-color);
        }

        input:focus + .slider {
            box-shadow: 0 0 1px var(--primary-color);
        }

        input:checked + .slider:before {
            transform: translateX(20px);
        }

        /* 表格样式 */
        .table-container {
            overflow-x: auto;
            margin-bottom: 20px;
        }

        .data-table {
            width: 100%;
            border-collapse: collapse;
        }

        .data-table th {
            background-color: #fafafa;
            padding: 12px 15px;
            text-align: left;
            font-weight: 500;
            color: #333;
            border-bottom: 1px solid #f0f0f0;
        }

        .data-table td {
            padding: 12px 15px;
            border-bottom: 1px solid #f0f0f0;
        }

        .data-table tr:hover {
            background-color: #f5f7fa;
        }

        /* 按钮样式 */
        .btn-group {
            display: flex;
            gap: 10px;
            margin-top: 20px;
            justify-content: flex-end;
        }

        .btn {
            padding: 8px 16px;
            border-radius: 4px;
            font-size: 14px;
            font-weight: 500;
            cursor: pointer;
            transition: all 0.3s;
            border: none;
        }

        .btn-primary {
            background-color: var(--primary-color);
            color: white;
        }

        .btn-primary:hover {
            background-color: #40a9ff;
            box-shadow: 0 2px 8px rgba(24, 144, 255, 0.3);
        }

        .btn-secondary {
            background-color: #f0f0f0;
            color: #666;
        }

        .btn-secondary:hover {
            background-color: #e0e0e0;
        }

        .btn-danger {
            background-color: #ff4d4f;
            color: white;
        }

        .btn-danger:hover {
            background-color: #ff7875;
            box-shadow: 0 2px 8px rgba(255, 77, 79, 0.3);
        }

        /* 滑块样式 */
        .range-slider {
            width: 100%;
            margin: 10px 0;
        }

        .range-slider-container {
            display: flex;
            align-items: center;
            gap: 10px;
        }

        .range-slider-value {
            width: 60px;
            text-align: center;
            font-weight: 500;
        }

        /* 参数卡片样式 */
        .param-cards {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(280px, 1fr));
            gap: 20px;
            margin-bottom: 20px;
        }

        .param-card {
            background-color: #fff;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);
            padding: 15px;
            transition: all 0.3s;
            border: 1px solid #f0f0f0;
        }

        .param-card:hover {
            box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
            transform: translateY(-2px);
        }

        .param-card.active {
            border-color: var(--primary-color);
        }

        .param-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 15px;
            padding-bottom: 10px;
            border-bottom: 1px solid #f0f0f0;
        }

        .param-name {
            font-weight: 600;
            color: #333;
            display: flex;
            align-items: center;
            gap: 8px;
        }

        .param-actions {
            display: flex;
            justify-content: flex-end;
            gap: 10px;
            margin-top: 15px;
        }

        /* 响应式调整 */
        @media (max-width: 768px) {
            .form-row {
                flex-direction: column;
                gap: 15px;
            }

            .form-group {
                min-width: 100%;
            }

            .submenu {
                overflow-x: auto;
                padding: 10px;
                flex-wrap: nowrap;
            }

            .submenu-item {
                white-space: nowrap;
            }

            .param-cards {
                grid-template-columns: 1fr;
            }
        }
    </style>
</head>
<body>
<script>document.addEventListener("DOMContentLoaded", function() { loadCommonElements(); });</script>
    <div class="container">
        <!-- 顶部栏 -->
        <div class="topbar">
            <div class="topbar-left">
                <div class="toggle-sidebar" onclick="toggleSidebar()">
                    <i class="fas fa-bars"></i>
                </div>
                <img src="logo.png" alt="公司Logo" class="topbar-logo" style="height: 60px; margin-right: 15px; transition: transform 0.3s;" onmouseover="this.style.transform='scale(1.05)'" onmouseout="this.style.transform='scale(1)'">
                <h2 class="topbar-title">精准曝气控制系统</h2>
            </div>
            <div class="topbar-right">
                <div class="user-dropdown">
                    <div class="user-info">
                        <div class="user-avatar">管</div>
                        <span>管理员</span>
                        <i class="fas fa-chevron-down" style="margin-left: 5px; font-size: 12px;"></i>
                    </div>
                    <div class="dropdown-content">
                        <a href="#"><i class="fas fa-user-circle"></i> 个人信息</a>
                        <a href="#"><i class="fas fa-cog"></i> 账号设置</a>
                        <a href="index.html" onclick="logout()"><i class="fas fa-sign-out-alt"></i> 退出登录</a>
                    </div>
                </div>
            </div>
        </div>

        <!-- 主体内容区域 -->
        <div class="main-container">
            <!-- 侧边栏 -->
            <div class="sidebar">
                <div class="sidebar-menu">
                    <div class="menu-item" id="menu-dashboard" onclick="window.location.href='dashboard.html'">
                        <i class="fas fa-tachometer-alt"></i>
                        <span>实时监控</span>
                    </div>
                    <div class="menu-item" id="menu-model" onclick="window.location.href='model-control.html'">
                        <i class="fas fa-brain"></i>
                        <span>模型控制</span>
                    </div>
                    <div class="menu-item active" id="menu-scheduling" onclick="window.location.href='scheduling.html'">
                        <i class="fas fa-tasks"></i>
                        <span>调度管理</span>
                    </div>
                    <div class="menu-item" id="menu-reports" onclick="window.location.href='reports.html'">
                        <i class="fas fa-chart-bar"></i>
                        <span>数据报表</span>
                    </div>
                    <div class="menu-item" id="menu-alarms" onclick="window.location.href='alarms.html'">
                        <i class="fas fa-bell"></i>
                        <span>告警管理</span>
                    </div>
                    <div class="menu-item" id="menu-settings" onclick="window.location.href='settings.html'">
                        <i class="fas fa-cog"></i>
                        <span>系统配置</span>
                    </div>
                </div>
            </div>

            <!-- 内容区域 -->
            <div class="content">
                <!-- 子菜单导航 -->
                <div class="submenu">
                    <div class="submenu-item" onclick="window.location.href='scheduling.html'">
                        <i class="fas fa-home"></i> 调度总览
                    </div>
                    <div class="submenu-item" onclick="window.location.href='device-scheduling.html'">
                        <i class="fas fa-fan"></i> 曝气设备调度
                    </div>
                    <div class="submenu-item active" onclick="window.location.href='do-parameter-scheduling.html'">
                        <i class="fas fa-sliders-h"></i> DO控制参数调度
                    </div>
                    <div class="submenu-item" onclick="window.location.href='energy-optimization.html'">
                        <i class="fas fa-bolt"></i> 能源优化调度
                    </div>
                    <div class="submenu-item" onclick="window.location.href='plan-management.html'">
                        <i class="fas fa-calendar-alt"></i> 调度计划管理
                    </div>
                    <div class="submenu-item" onclick="window.location.href='execution-monitoring.html'">
                        <i class="fas fa-desktop"></i> 执行监控
                    </div>
                    <div class="submenu-item" onclick="window.location.href='statistics-reports.html'">
                        <i class="fas fa-chart-pie"></i> 统计报表
                    </div>
                </div>

                <!-- 页面标题 -->
                <div class="page-header">
                    <h1 class="page-title">
                        <i class="fas fa-sliders-h" style="color: var(--primary-color);"></i>
                        DO控制参数调度
                    </h1>
                    <div class="page-actions">
                        <button class="btn btn-primary" onclick="saveAllSettings()">
                            <i class="fas fa-save"></i> 保存所有设置
                        </button>
                    </div>
                </div>

                <!-- 标签页容器 -->
                <div class="tab-container">
                    <div class="tab-header">
                        <div class="tab-item active" data-tab="do-target">
                            <i class="fas fa-bullseye"></i> DO目标值设定
                        </div>
                        <div class="tab-item" data-tab="control-params">
                            <i class="fas fa-sliders-h"></i> 控制参数调整
                        </div>
                        <div class="tab-item" data-tab="day-night-mode">
                            <i class="fas fa-moon"></i> 日/夜模式切换
                        </div>
                    </div>

                    <!-- DO目标值设定 -->
                    <div class="tab-content active" id="do-target">
                        <!-- 时段划分 -->
                        <div class="form-section">
                            <h3 class="section-title">
                                <i class="fas fa-clock" style="color: #1890ff;"></i>
                                时段划分
                            </h3>
                            <div class="form-row">
                                <div class="form-group">
                                    <label class="form-label">时段划分方案</label>
                                    <select class="form-control" id="time-division-scheme">
                                        <option value="3-period" selected>三时段（早、中、晚）</option>
                                        <option value="4-period">四时段（凌晨、上午、下午、晚上）</option>
                                        <option value="hourly">按小时划分</option>
                                        <option value="custom">自定义时段</option>
                                    </select>
                                </div>
                                <div class="form-group">
                                    <label class="form-label">方案状态</label>
                                    <div style="display: flex; align-items: center; gap: 10px;">
                                        <label class="switch">
                                            <input type="checkbox" checked>
                                            <span class="slider"></span>
                                        </label>
                                        <span>启用时段划分</span>
                                    </div>
                                </div>
                            </div>

                            <div id="three-period-settings">
                                <div class="form-row">
                                    <div class="form-group">
                                        <label class="form-label">早晨时段</label>
                                        <div class="time-picker">
                                            <input type="time" class="form-control" value="06:00">
                                            <span>至</span>
                                            <input type="time" class="form-control" value="12:00">
                                        </div>
                                    </div>
                                </div>
                                <div class="form-row">
                                    <div class="form-group">
                                        <label class="form-label">中午时段</label>
                                        <div class="time-picker">
                                            <input type="time" class="form-control" value="12:00">
                                            <span>至</span>
                                            <input type="time" class="form-control" value="18:00">
                                        </div>
                                    </div>
                                </div>
                                <div class="form-row">
                                    <div class="form-group">
                                        <label class="form-label">晚上时段</label>
                                        <div class="time-picker">
                                            <input type="time" class="form-control" value="18:00">
                                            <span>至</span>
                                            <input type="time" class="form-control" value="06:00">
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <div id="custom-period-settings" style="display: none;">
                                <div class="form-row">
                                    <div class="form-group">
                                        <button class="btn btn-secondary" onclick="addTimePeriod()">
                                            <i class="fas fa-plus"></i> 添加时段
                                        </button>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- 时段DO值 -->
                        <div class="form-section">
                            <h3 class="section-title">
                                <i class="fas fa-water" style="color: #52c41a;"></i>
                                时段DO值
                            </h3>
                            <div class="table-container">
                                <table class="data-table">
                                    <thead>
                                        <tr>
                                            <th>时段</th>
                                            <th>时间范围</th>
                                            <th>DO目标值 (mg/L)</th>
                                            <th>备注</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <tr>
                                            <td>早晨</td>
                                            <td>06:00 - 12:00</td>
                                            <td>
                                                <input type="number" class="form-control" value="2.5" min="0" max="10" step="0.1" style="width: 80px;">
                                            </td>
                                            <td>进水负荷上升阶段</td>
                                        </tr>
                                        <tr>
                                            <td>中午</td>
                                            <td>12:00 - 18:00</td>
                                            <td>
                                                <input type="number" class="form-control" value="3.0" min="0" max="10" step="0.1" style="width: 80px;">
                                            </td>
                                            <td>高负荷阶段</td>
                                        </tr>
                                        <tr>
                                            <td>晚上</td>
                                            <td>18:00 - 06:00</td>
                                            <td>
                                                <input type="number" class="form-control" value="1.8" min="0" max="10" step="0.1" style="width: 80px;">
                                            </td>
                                            <td>低负荷阶段，节能运行</td>
                                        </tr>
                                    </tbody>
                                </table>
                            </div>
                        </div>

                        <!-- 季节性调整 -->
                        <div class="form-section">
                            <h3 class="section-title">
                                <i class="fas fa-sun" style="color: #fa8c16;"></i>
                                季节性调整
                            </h3>
                            <div class="form-row">
                                <div class="form-group">
                                    <label class="form-label">季节性调整状态</label>
                                    <div style="display: flex; align-items: center; gap: 10px;">
                                        <label class="switch">
                                            <input type="checkbox" checked>
                                            <span class="slider"></span>
                                        </label>
                                        <span>启用季节性调整</span>
                                    </div>
                                </div>
                            </div>

                            <div class="table-container">
                                <table class="data-table">
                                    <thead>
                                        <tr>
                                            <th>季节</th>
                                            <th>月份</th>
                                            <th>DO基准值调整系数</th>
                                            <th>备注</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <tr>
                                            <td>春季</td>
                                            <td>3-5月</td>
                                            <td>
                                                <input type="number" class="form-control" value="1.0" min="0.5" max="2.0" step="0.1" style="width: 80px;">
                                            </td>
                                            <td>标准系数</td>
                                        </tr>
                                        <tr>
                                            <td>夏季</td>
                                            <td>6-8月</td>
                                            <td>
                                                <input type="number" class="form-control" value="1.2" min="0.5" max="2.0" step="0.1" style="width: 80px;">
                                            </td>
                                            <td>高温季节，微生物活性高</td>
                                        </tr>
                                        <tr>
                                            <td>秋季</td>
                                            <td>9-11月</td>
                                            <td>
                                                <input type="number" class="form-control" value="1.0" min="0.5" max="2.0" step="0.1" style="width: 80px;">
                                            </td>
                                            <td>标准系数</td>
                                        </tr>
                                        <tr>
                                            <td>冬季</td>
                                            <td>12-2月</td>
                                            <td>
                                                <input type="number" class="form-control" value="0.8" min="0.5" max="2.0" step="0.1" style="width: 80px;">
                                            </td>
                                            <td>低温季节，微生物活性低</td>
                                        </tr>
                                    </tbody>
                                </table>
                            </div>
                        </div>

                        <!-- 水温关联 -->
                        <div class="form-section">
                            <h3 class="section-title">
                                <i class="fas fa-thermometer-half" style="color: #722ed1;"></i>
                                水温关联
                            </h3>
                            <div class="form-row">
                                <div class="form-group">
                                    <label class="form-label">水温关联状态</label>
                                    <div style="display: flex; align-items: center; gap: 10px;">
                                        <label class="switch">
                                            <input type="checkbox" checked>
                                            <span class="slider"></span>
                                        </label>
                                        <span>启用水温关联调整</span>
                                    </div>
                                </div>
                                <div class="form-group">
                                    <label class="form-label">水温数据来源</label>
                                    <select class="form-control">
                                        <option>在线水温传感器</option>
                                        <option>手动输入</option>
                                        <option>气象数据API</option>
                                    </select>
                                </div>
                            </div>

                            <div class="form-row">
                                <div class="form-group">
                                    <label class="form-label">水温-DO关系曲线</label>
                                    <div class="table-container">
                                        <table class="data-table">
                                            <thead>
                                                <tr>
                                                    <th>水温范围 (°C)</th>
                                                    <th>DO调整系数</th>
                                                </tr>
                                            </thead>
                                            <tbody>
                                                <tr>
                                                    <td>≤ 10</td>
                                                    <td>
                                                        <input type="number" class="form-control" value="0.7" min="0.5" max="2.0" step="0.1" style="width: 80px;">
                                                    </td>
                                                </tr>
                                                <tr>
                                                    <td>10 - 15</td>
                                                    <td>
                                                        <input type="number" class="form-control" value="0.8" min="0.5" max="2.0" step="0.1" style="width: 80px;">
                                                    </td>
                                                </tr>
                                                <tr>
                                                    <td>15 - 20</td>
                                                    <td>
                                                        <input type="number" class="form-control" value="1.0" min="0.5" max="2.0" step="0.1" style="width: 80px;">
                                                    </td>
                                                </tr>
                                                <tr>
                                                    <td>20 - 25</td>
                                                    <td>
                                                        <input type="number" class="form-control" value="1.2" min="0.5" max="2.0" step="0.1" style="width: 80px;">
                                                    </td>
                                                </tr>
                                                <tr>
                                                    <td>≥ 25</td>
                                                    <td>
                                                        <input type="number" class="form-control" value="1.3" min="0.5" max="2.0" step="0.1" style="width: 80px;">
                                                    </td>
                                                </tr>
                                            </tbody>
                                        </table>
                                    </div>
                                </div>
                            </div>

                            <div class="form-text">系统将根据当前水温自动调整DO目标值，最终DO目标值 = 时段DO值 × 季节调整系数 × 水温调整系数</div>
                        </div>

                        <div class="btn-group">
                            <button class="btn btn-secondary">取消</button>
                            <button class="btn btn-primary" onclick="saveDOTargetSettings()">保存设置</button>
                        </div>
                    </div>

                    <!-- 控制参数调整标签页 -->
                    <div class="tab-content" id="control-params">
                        <!-- 内容将在下一步添加 -->
                    </div>

                    <!-- 日/夜模式切换标签页 -->
                    <div class="tab-content" id="day-night-mode">
                        <!-- 内容将在下一步添加 -->
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="js/common.js"></script>
    <script>
        // 初始化公共元素
        document.addEventListener('DOMContentLoaded', function() {
            // 这里可以添加页面初始化代码
        });

        // 保存所有设置
        function saveAllSettings() {
            showNotification('正在保存所有设置...', 'info');

            // 模拟保存延迟
            setTimeout(function() {
                showNotification('所有设置已保存', 'success');
            }, 1500);
        }

        // 显示通知
        function showNotification(message, type) {
            // 假设common.js中已经实现了这个函数
            if (window.showNotification) {
                window.showNotification(message, type);
            } else {
                alert(message);
            }
        }
    </script>
</body>
</html>
